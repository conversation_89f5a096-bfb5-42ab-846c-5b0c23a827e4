[project]
name = "crisprprimer"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "biopython>=1.85",
    "biov>=0.1.2",
    "fsspec[full]>=2025.5.0",
    "gradio>=5.31.0",
    "gradio-molecule3d>=0.0.7",
    "jupyter-collaboration>=4.0.2",
    "jupyter-kernel-client>=0.6.0",
    "jupyter-nbmodel-client>=0.13.0",
    "jupyterlab>=4.4.2",
    "jupytext>=1.17.1",
    "mcp>=1.9.1",
    "mkdocs>=1.6.1",
    "mkdocs-gen-files>=0.5.0",
    "mkdocs-jupyter>=0.25.1",
    "mkdocs-literate-nav>=0.6.2",
    "mkdocs-material>=9.6.14",
    "mkdocstrings[python]>=0.29.1",
    "openai>=1.82.0",
    "pandas>=2.2.3",
    "pytest-cov>=6.2.1",
    "seaborn>=0.13.2",
    "swarmx>=0.6.1",
    "tabulate>=0.9.0",
    "tqdm>=4.67.1",
    "typer>=0.15.4",
]

[tool.uv.sources]
biov = { path = "../biov", editable = true }

[project.scripts]
crisprprimer = "crisprprimer.__main__:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.sdist]
include = [
    "crisprprimer/*.txt.gz",
]

[tool.mypy]
plugins = ['pydantic.mypy']

[tool.ruff]
line-length = 120

[tool.ruff.lint]
extend-select = ["I", "T20"]

[dependency-groups]
dev = [
    "rpy2>=3.5.17",
    "pytest>=8.3.5",
    "dask>=2025.3.0",
    "ipykernel>=6.29.5",
    "ipywidgets>=8.1.7",
    "openpyxl>=3.1.5",
]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q"
testpaths = [
    "tests",
    "integration",
]

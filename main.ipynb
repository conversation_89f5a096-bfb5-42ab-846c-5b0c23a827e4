{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3c23e6b2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/crisprprimer/.venv/lib/python3.11/site-packages/ncls/__init__.py:2: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.\n", "  import pkg_resources\n", "  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:41<00:00, 41.85s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import pandas as pd\n", "from pathlib import Path\n", "from crisprprimer import crisprprimer\n", "from biov import read_fasta\n", "import pandas as pd\n", "from Bio.Seq import reverse_complement\n", "from biov import read_fasta\n", "\n", "fourth_round = pd.read_table(\n", "    \"/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/7c0d59a216da57bd3f97493234b78677/Message/MessageTemp/3618242fc030127d5802de246d5515a9/File/geneall.txt\",\n", "    names=(\"ID\", \"locus_id\", \"type\", \"seqid\", \"unknown1\", \"unknown2\", \"unknown3\", \"spacer1\", \"spacer2\", \"target1\", \"target2\"),\n", "    index_col=0\n", ")\n", "fifth_round_planning = pd.read_excel(\"/Users/<USER>/Downloads/20250517 第五轮载体构建 任务规划V1.xlsx\", sheet_name=\"第五轮拟构建载体（去重后）\")\n", "crisprprimer(\n", "    # regions=fifth_round_planning[\"基因代码\"].map(fourth_round[\"locus_id\"].to_dict()).to_list(),\n", "    regions=[\"LOC_Os12g43410\"],\n", "    blocks_dir=Path(\"/Volumes/TOSHIBA/rice_crispr/irgsp1_nj7\"),\n", "    ind=\"NJ7_ref/res.fa\",\n", "    ind_blat_cache_dir=Path(\"/Volumes/TOSHIBA/rice_crispr/blat/nj7\"),\n", "    ref_blat_cache_dir=Path(\"/Volumes/TOSHIBA/rice_crispr/blat/irgsp1\"),\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "31c6ff0d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:00<00:00,  1.95it/s]\n"]}], "source": ["from importlib import reload\n", "import crisprprimer\n", "reload(crisprprimer)\n", "from crisprprimer import crisprprimer\n", "\n", "crisprprimer(\n", "    # regions=fifth_round_planning[\"基因代码\"].map(fourth_round[\"locus_id\"].to_dict()).to_list(),\n", "    regions=[\"LOC_Os12g43430\"],\n", "    blocks_dir=Path(\"/Volumes/TOSHIBA/rice_crispr/irgsp1_nj7\"),\n", "    ind=\"NJ7_ref/res.fa\",\n", "    ind_blat_cache_dir=Path(\"/Volumes/TOSHIBA/rice_crispr/blat/nj7\"),\n", "    ref_blat_cache_dir=Path(\"/Volumes/TOSHIBA/rice_crispr/blat/irgsp1\"),\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "a0aa3419", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>nuclease</th>\n", "      <th>seqid</th>\n", "      <th>locus_id</th>\n", "      <th>ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>466</th>\n", "      <td>26963766</td>\n", "      <td>26963786</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GCCAACTCCGCACCCCCCCC</td>\n", "      <td>SpCas9</td>\n", "      <td>Chr12</td>\n", "      <td>LOC_Os12g43460</td>\n", "      <td>Chr12:26963767..26963786 (-strand)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer nuclease  seqid  \\\n", "466  26963766  26963786  GGG      -  GCCAACTCCGCACCCCCCCC   SpCas9  Chr12   \n", "\n", "           locus_id                                  ID  \n", "466  LOC_Os12g43460  Chr12:26963767..26963786 (-strand)  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet(\"/Volumes/TOSHIBA/rice_crispr/irgsp1_nj7/spacers/seqid=Chr12/LOC_Os12g43460.parquet\")"]}, {"cell_type": "code", "execution_count": 2, "id": "c8072e07", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.True_"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pairs = pd.read_csv(\"fifth.csv\", index_col=0).dropna(subset=\"spacer2\")\n", "# pairs[\"window_size\"] = pairs[\"window_size\"].astype(int)\n", "pairs[\"spacer1_with_pam\"] = pairs[\"spacer1\"].apply(get_spacer_with_pam)\n", "pairs[\"spacer2_with_pam\"] = pairs[\"spacer2\"].apply(get_spacer_with_pam)\n", "pairs[\"spacer1_with_linker\"] = \"gcggtctcaggcg\" + pairs[\"spacer1_seq\"] + \"gttttagagctagaaatagcaag\"\n", "pairs[\"spacer2_with_linker\"] = \"ttggtctctaaac\" + pairs[\"spacer2_seq\"].apply(reverse_complement)+ \"cacacaagcgacagc\"\n", "pairs[\"基因代码\"] = pairs.index.map({v: k for k, v in fourth_round[\"locus_id\"].to_dict().items()})\n", "pairs.reset_index(inplace=True, names=\"locus_id\")\n", "(\n", "    pairs[\"spacer1_with_pam\"].str.endswith(\"GG\")  # 这里验证的是是否是合法的Spacer\n", "    & pairs[\"spacer2_with_pam\"].str.endswith(\"GG\")\n", "    & ~pairs[\"spacer1_with_linker\"].str.upper().str.contains(\"GAGACC\")\n", "    & ~pairs[\"spacer2_with_linker\"].str.upper().str.contains(\"GAGACC\")\n", "    & ~pairs[\"spacer1_with_linker\"].str[8:].str.upper().str.contains(\"GGTCTC\")  # 去掉前8个是因为前8个包含GGTCTC\n", "    & ~pairs[\"spacer2_with_linker\"].str[8:].str.upper().str.contains(\"GGTCTC\")\n", ").all()"]}, {"cell_type": "code", "execution_count": 10, "id": "86824d95", "metadata": {}, "outputs": [], "source": ["import json\n", "with open(\"crisprprimer/nju_id_map.json\", \"w\") as f:\n", "    json.dump(fourth_round[\"locus_id\"].to_dict(), f)"]}, {"cell_type": "code", "execution_count": 13, "id": "74467170", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 67%|██████▋   | 2989/4444 [00:09<00:04, 329.95it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 68%|██████▊   | 3006/4444 [00:24<03:43,  6.44it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 68%|██████▊   | 3006/4444 [00:29<00:13, 103.20it/s]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 79\u001b[39m\n\u001b[32m     77\u001b[39m     f.flush()\n\u001b[32m     78\u001b[39m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m79\u001b[39m result = \u001b[43mblat_n7\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrow_\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mspacer2_seq\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     80\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(result.query(\u001b[33m\"\u001b[39m\u001b[33mmatches == 20 and blockCount == 1\u001b[39m\u001b[33m\"\u001b[39m)) != \u001b[32m1\u001b[39m:\n\u001b[32m     81\u001b[39m     \u001b[38;5;28mprint\u001b[39m(row_[\u001b[33m\"\u001b[39m\u001b[33mlocus_id\u001b[39m\u001b[33m\"\u001b[39m], file=f)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 27\u001b[39m, in \u001b[36mblat_n7\u001b[39m\u001b[34m(query)\u001b[39m\n\u001b[32m     25\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m pd.read_parquet(fp)\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m27\u001b[39m     result = \u001b[43mblat\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mNJ7_ref/res.fa\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mquery\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mminMatch\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mminScore\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m18\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstepSize\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m5\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfine\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     28\u001b[39m     result.to_parquet(fp)\n\u001b[32m     29\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32m~/biov/src/biov/executables/_blat.py:458\u001b[39m, in \u001b[36mblat\u001b[39m\u001b[34m(database, query, **kwargs)\u001b[39m\n\u001b[32m    456\u001b[39m     query = queries\n\u001b[32m    457\u001b[39m f = stack.enter_context(NamedTemporaryFile(mode=\u001b[33m\"\u001b[39m\u001b[33mw+t\u001b[39m\u001b[33m\"\u001b[39m))\n\u001b[32m--> \u001b[39m\u001b[32m458\u001b[39m \u001b[43msubprocess\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    459\u001b[39m \u001b[43m    \u001b[49m\u001b[43m[\u001b[49m\n\u001b[32m    460\u001b[39m \u001b[43m        \u001b[49m\u001b[43mblat_exec\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    461\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m-noHead\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    462\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m[\u001b[49m\n\u001b[32m    463\u001b[39m \u001b[43m            \u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m-\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mk\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mbool\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m-\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mk\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m=\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mv\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\n\u001b[32m    464\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    465\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[32m    466\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mk\u001b[49m\u001b[43m \u001b[49m\u001b[43m!=\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mnoHead\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\n\u001b[32m    467\u001b[39m \u001b[43m        \u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    468\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdatabase\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    469\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    470\u001b[39m \u001b[43m        \u001b[49m\u001b[43mf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    471\u001b[39m \u001b[43m    \u001b[49m\u001b[43m]\u001b[49m\n\u001b[32m    472\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    473\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m pd.read_table(\n\u001b[32m    474\u001b[39m     f,\n\u001b[32m    475\u001b[39m     names=_BLAT_COLUMNS,\n\u001b[32m    476\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/share/uv/python/cpython-3.11.10-macos-aarch64-none/lib/python3.11/subprocess.py:550\u001b[39m, in \u001b[36mrun\u001b[39m\u001b[34m(input, capture_output, timeout, check, *popenargs, **kwargs)\u001b[39m\n\u001b[32m    548\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Popen(*popenargs, **kwargs) \u001b[38;5;28;01mas\u001b[39;00m process:\n\u001b[32m    549\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m550\u001b[39m         stdout, stderr = \u001b[43mprocess\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcommunicate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    551\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m TimeoutExpired \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    552\u001b[39m         process.kill()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/share/uv/python/cpython-3.11.10-macos-aarch64-none/lib/python3.11/subprocess.py:1201\u001b[39m, in \u001b[36mPopen.communicate\u001b[39m\u001b[34m(self, input, timeout)\u001b[39m\n\u001b[32m   1199\u001b[39m         stderr = \u001b[38;5;28mself\u001b[39m.stderr.read()\n\u001b[32m   1200\u001b[39m         \u001b[38;5;28mself\u001b[39m.stderr.close()\n\u001b[32m-> \u001b[39m\u001b[32m1201\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mwait\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1202\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1203\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/share/uv/python/cpython-3.11.10-macos-aarch64-none/lib/python3.11/subprocess.py:1264\u001b[39m, in \u001b[36mPopen.wait\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m   1262\u001b[39m     endtime = _time() + timeout\n\u001b[32m   1263\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1264\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_wait\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1265\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[32m   1266\u001b[39m     \u001b[38;5;66;03m# https://bugs.python.org/issue25942\u001b[39;00m\n\u001b[32m   1267\u001b[39m     \u001b[38;5;66;03m# The first keyboard interrupt waits briefly for the child to\u001b[39;00m\n\u001b[32m   1268\u001b[39m     \u001b[38;5;66;03m# exit under the common assumption that it also received the ^C\u001b[39;00m\n\u001b[32m   1269\u001b[39m     \u001b[38;5;66;03m# generated SIGINT and will exit rapidly.\u001b[39;00m\n\u001b[32m   1270\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/share/uv/python/cpython-3.11.10-macos-aarch64-none/lib/python3.11/subprocess.py:2053\u001b[39m, in \u001b[36mPopen._wait\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m   2051\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.returncode \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   2052\u001b[39m     \u001b[38;5;28;01mbreak\u001b[39;00m  \u001b[38;5;66;03m# Another thread waited.\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m2053\u001b[39m (pid, sts) = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_try_wait\u001b[49m\u001b[43m(\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m   2054\u001b[39m \u001b[38;5;66;03m# Check the pid and loop as waitpid has been known to\u001b[39;00m\n\u001b[32m   2055\u001b[39m \u001b[38;5;66;03m# return 0 even without <PERSON>N<PERSON><PERSON><PERSON> in odd situations.\u001b[39;00m\n\u001b[32m   2056\u001b[39m \u001b[38;5;66;03m# http://bugs.python.org/issue14396.\u001b[39;00m\n\u001b[32m   2057\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m pid == \u001b[38;5;28mself\u001b[39m.pid:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/share/uv/python/cpython-3.11.10-macos-aarch64-none/lib/python3.11/subprocess.py:2011\u001b[39m, in \u001b[36mPopen._try_wait\u001b[39m\u001b[34m(self, wait_flags)\u001b[39m\n\u001b[32m   2009\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"All callers to this function MUST hold self._waitpid_lock.\"\"\"\u001b[39;00m\n\u001b[32m   2010\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m2011\u001b[39m     (pid, sts) = os.waitpid(\u001b[38;5;28mself\u001b[39m.pid, wait_flags)\n\u001b[32m   2012\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mChildProcessError\u001b[39;00m:\n\u001b[32m   2013\u001b[39m     \u001b[38;5;66;03m# This happens if SIGCLD is set to be ignored or waiting\u001b[39;00m\n\u001b[32m   2014\u001b[39m     \u001b[38;5;66;03m# for child processes has otherwise been disabled for our\u001b[39;00m\n\u001b[32m   2015\u001b[39m     \u001b[38;5;66;03m# process.  This child is dead, we can't get the status.\u001b[39;00m\n\u001b[32m   2016\u001b[39m     pid = \u001b[38;5;28mself\u001b[39m.pid\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["from biov.executables import blat\n", "from Bio.SeqRecord import SeqRecord\n", "import tqdm\n", "\n", "from itertools import islice\n", "nj7 = read_fasta(\"NJ7_ref/res.fa\")\n", "def batched(iterable, n, *, strict=False):\n", "    # batched('ABCDEFG', 3) → ABC DEF G\n", "    if n < 1:\n", "        raise ValueError(\"n must be at least one\")\n", "    iterator = iter(iterable)\n", "    while batch := tuple(islice(iterator, n)):\n", "        if strict and len(batch) != n:\n", "            raise ValueError(\"batched(): incomplete batch\")\n", "        yield batch\n", "\n", "def blat_n7(query: str):\n", "    parts = list(batched(query, 5))\n", "    parent = Path(\"NJ7_blat\")\n", "    for part in parts[:-1]:\n", "        parent /= \"\".join(part)\n", "    parent.mkdir(parents=True, exist_ok=True)\n", "    fp = parent / f\"{''.join(parts[-1])}.parquet\"\n", "    if fp.exists():\n", "        return pd.read_parquet(fp)\n", "    else:\n", "        result = blat(\"NJ7_ref/res.fa\", query, minMatch=0, minScore=18, stepSize=5, fine=True)\n", "        result.to_parquet(fp)\n", "        return result\n", "\n", "\n", "def get_pam(seqs: dict[str, SeqRecord], tName: str, tStart: int, tEnd: int, strand: str):\n", "    if strand == \"+\":\n", "        return str(seqs[tName][tEnd + 1 : tEnd + 3].seq).upper()\n", "    else:\n", "        return str(seqs[tName][tStart - 3 : tStart - 1].seq.reverse_complement()).upper()\n", "\n", "\n", "def get_protospacer(seqs: dict[str, SeqRecord], row):\n", "    tName = row[\"tName\"]\n", "    strand = row[\"strand\"]\n", "    if row[\"blockCount\"] == 2:\n", "        blockSizes = [int(size) for size in row[\"blockSizes\"].split(\",\")[:-1]]\n", "        qStarts = [int(start) for start in row[\"qStarts\"].split(\",\")[:-1]]\n", "        tStarts = [int(start) for start in row[\"tStarts\"].split(\",\")[:-1]]\n", "        # get biggest block's qStart & tStart\n", "        if blockSizes[0] > blockSizes[1]:\n", "            qStart = qStarts[0]\n", "            qEnd = qStart + blockSizes[0]\n", "            tStart = tStarts[0]\n", "            tEnd = tStart + blockSizes[0]\n", "        else:\n", "            qStart = qStarts[1]\n", "            qEnd = qStarts[1] + blockSizes[1]\n", "            tStart = tStarts[1]\n", "            tEnd = tStart + blockSizes[1]\n", "    else:\n", "        tStart = row[\"tStart\"]\n", "        tEnd = row[\"tEnd\"]\n", "\n", "        qStart = row[\"qStart\"]\n", "        qEnd = row[\"qEnd\"]\n", "    if strand == \"+\":\n", "        return str(seqs[tName][tStart - qStart : tEnd - qEnd + 20].seq).upper()\n", "    else:\n", "        return str(seqs[tName][tStart - qStart : tEnd - qEnd + 20].seq.reverse_complement()).upper()\n", "\n", "with open(\"need_redesign.txt\", \"w\") as f:\n", "    for i, row_ in tqdm.tqdm(pairs.iterrows(), total=len(pairs)):\n", "        result = blat_n7(row_[\"spacer1_seq\"])\n", "        if len(result.query(\"matches == 20 and blockCount == 1\")) != 1:\n", "            print(row_[\"locus_id\"], file=f)\n", "            f.flush()\n", "            continue\n", "        elif get_pam(nj7, (row := result.iloc[0])[\"tName\"], row[\"tStart\"], row[\"tEnd\"], row[\"strand\"]) != \"GG\":\n", "            print(row_[\"locus_id\"], file=f)\n", "            f.flush()\n", "            continue\n", "        result = blat_n7(row_[\"spacer2_seq\"])\n", "        if len(result.query(\"matches == 20 and blockCount == 1\")) != 1:\n", "            print(row_[\"locus_id\"], file=f)\n", "            f.flush()\n", "            continue\n", "        elif get_pam(nj7, (row := result.iloc[0])[\"tName\"], row[\"tStart\"], row[\"tEnd\"], row[\"strand\"]) != \"GG\":\n", "            print(row_[\"locus_id\"], file=f)\n", "            f.flush()\n", "            continue\n", "result"]}, {"cell_type": "code", "execution_count": 15, "id": "8fac3c3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['mRNA', 'five_prime_UTR', 'CDS', 'three_prime_UTR', 'exon'],\n", "      dtype=object)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_gff3\n", "\n", "read_gff3(\n", "    \"tar://IRGSP-1.0_representative/transcripts.gff::\"\n", "    \"https://rapdb.dna.affrc.go.jp/download/archive/irgsp1/\"\n", "    \"IRGSP-1.0_representative_2025-03-19.tar.gz\"\n", ")[\"type\"].unique()"]}, {"cell_type": "code", "execution_count": 46, "id": "04ac2dec", "metadata": {}, "outputs": [], "source": ["pairs[[\"基因代码\", \"seqid\", \"locus_id\", \"start\", \"end\", \"spacer1\", \"spacer2\", \"window_size\", \"score\", \"spacer1_seq\", \"spacer2_seq\", \"spacer1_with_linker\", \"spacer2_with_linker\", \"spacer1_gc_fraction\", \"spacer2_gc_fraction\"]].to_excel(\"/Users/<USER>/Desktop/pairs_with_linker.xlsx\", index=False)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "d11ad6d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["'TTG'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["get_seq(\"Chr4:15280606..15280608 (-strand)\")"]}, {"cell_type": "code", "execution_count": 26, "id": "d129d902", "metadata": {}, "outputs": [{"data": {"text/plain": ["'TCC'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["get_seq(\"Chr4:15280627..15280629 (-strand)\")"]}, {"cell_type": "code", "execution_count": 11, "id": "48fe1fab", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'A1': 'LOC_Os01g01010',\n", " 'A2': 'LOC_Os01g01019',\n", " 'A3': 'LOC_Os01g01030',\n", " 'A4': 'LOC_Os01g01040',\n", " 'A5': 'LOC_Os01g01050',\n", " 'A6': 'LOC_Os01g01060',\n", " 'A7': 'LOC_Os01g01070',\n", " 'A8': 'LOC_Os01g01080',\n", " 'A9': 'LOC_Os01g01090',\n", " 'A10': 'LOC_Os01g01100',\n", " 'A11': 'LOC_Os01g01110',\n", " 'A12': 'LOC_Os01g01115',\n", " 'A13': 'LOC_Os01g01120',\n", " 'A14': 'LOC_Os01g01130',\n", " 'A15': 'LOC_Os01g01140',\n", " 'A16': 'LOC_Os01g01150',\n", " 'A17': 'LOC_Os01g01160',\n", " 'A18': 'LOC_Os01g01170',\n", " 'A19': 'LOC_Os01g01180',\n", " 'A20': 'LOC_Os01g01190',\n", " 'A21': 'LOC_Os01g01200',\n", " 'A22': 'LOC_Os01g01210',\n", " 'A23': 'LOC_Os01g01230',\n", " 'A24': 'LOC_Os01g01240',\n", " 'A25': 'LOC_Os01g01250',\n", " 'A26': 'LOC_Os01g01260',\n", " 'A27': 'LOC_Os01g01270',\n", " 'A28': 'LOC_Os01g01280',\n", " 'A29': 'LOC_Os01g01290',\n", " 'A30': 'LOC_Os01g01295',\n", " 'A31': 'LOC_Os01g01302',\n", " 'A32': 'LOC_Os01g01307',\n", " 'A33': 'LOC_Os01g01312',\n", " 'A34': 'LOC_Os01g01320',\n", " 'A35': 'LOC_Os01g01330',\n", " 'A36': 'LOC_Os01g01340',\n", " 'A37': 'LOC_Os01g01350',\n", " 'A38': 'LOC_Os01g01360',\n", " 'A39': 'LOC_Os01g01369',\n", " 'A40': 'LOC_Os01g01380',\n", " 'A41': 'LOC_Os01g01390',\n", " 'A42': 'LOC_Os01g01400',\n", " 'A43': 'LOC_Os01g01410',\n", " 'A44': 'LOC_Os01g01420',\n", " 'A45': 'LOC_Os01g01430',\n", " 'A46': 'LOC_Os01g01440',\n", " 'A47': 'LOC_Os01g01450',\n", " 'A48': 'LOC_Os01g01460',\n", " 'A49': 'LOC_Os01g01470',\n", " 'A50': 'LOC_Os01g01484',\n", " 'A51': 'LOC_Os01g01500',\n", " 'A52': 'LOC_Os01g01510',\n", " 'A53': 'LOC_Os01g01520',\n", " 'A54': 'LOC_Os01g01530',\n", " 'A55': 'LOC_Os01g01540',\n", " 'A56': 'LOC_Os01g01550',\n", " 'A57': 'LOC_Os01g01560',\n", " 'A58': 'LOC_Os01g01570',\n", " 'A59': 'LOC_Os01g01580',\n", " 'A60': 'LOC_Os01g01590',\n", " 'A61': 'LOC_Os01g01600',\n", " 'A62': 'LOC_Os01g01610',\n", " 'A63': 'LOC_Os01g01620',\n", " 'A64': 'LOC_Os01g01640',\n", " 'A65': 'LOC_Os01g01650',\n", " 'A66': 'LOC_Os01g01660',\n", " 'A67': 'LOC_Os01g01670',\n", " 'A68': 'LOC_Os01g01680',\n", " 'A69': 'LOC_Os01g01689',\n", " 'A70': 'LOC_Os01g01700',\n", " 'A71': 'LOC_Os01g01710',\n", " 'A72': 'LOC_Os01g01720',\n", " 'A73': 'LOC_Os01g01730',\n", " 'A74': 'LOC_Os01g01740',\n", " 'A75': 'LOC_Os01g01760',\n", " 'A76': 'LOC_Os01g01770',\n", " 'A77': 'LOC_Os01g01780',\n", " 'A78': 'LOC_Os01g01790',\n", " 'A79': 'LOC_Os01g01800',\n", " 'A80': 'LOC_Os01g01810',\n", " 'A81': 'LOC_Os01g01830',\n", " 'A82': 'LOC_Os01g01840',\n", " 'A83': 'LOC_Os01g01850',\n", " 'A84': 'LOC_Os01g01860',\n", " 'A85': 'LOC_Os01g01870',\n", " 'A86': 'LOC_Os01g01880',\n", " 'A87': 'LOC_Os01g01890',\n", " 'A88': 'LOC_Os01g01900',\n", " 'A89': 'LOC_Os01g01910',\n", " 'A90': 'LOC_Os01g01920',\n", " 'A91': 'LOC_Os01g01925',\n", " 'A92': 'LOC_Os01g01930',\n", " 'A93': 'LOC_Os01g01940',\n", " 'A94': 'LOC_Os01g01950',\n", " 'A95': 'LOC_Os01g01960',\n", " 'A96': 'LOC_Os01g01970',\n", " 'A97': 'LOC_Os01g01980',\n", " 'A98': 'LOC_Os01g01990',\n", " 'A99': 'LOC_Os01g02000',\n", " 'A100': 'LOC_Os01g02010',\n", " 'A101': 'LOC_Os01g02020',\n", " 'A102': 'LOC_Os01g02040',\n", " 'A103': 'LOC_Os01g02050',\n", " 'A104': 'LOC_Os01g02060',\n", " 'A105': 'LOC_Os01g02070',\n", " 'A106': 'LOC_Os01g02080',\n", " 'A107': 'LOC_Os01g02090',\n", " 'A108': 'LOC_Os01g02100',\n", " 'A109': 'LOC_Os01g02110',\n", " 'A110': 'LOC_Os01g02120',\n", " 'A111': 'LOC_Os01g02130',\n", " 'A112': 'LOC_Os01g02139',\n", " 'A113': 'LOC_Os01g02150',\n", " 'A114': 'LOC_Os01g02160',\n", " 'A115': 'LOC_Os01g02170',\n", " 'A116': 'LOC_Os01g02180',\n", " 'A117': 'LOC_Os01g02190',\n", " 'A118': 'LOC_Os01g02200',\n", " 'A119': 'LOC_Os01g02210',\n", " 'A120': 'LOC_Os01g02220',\n", " 'A121': 'LOC_Os01g02230',\n", " 'A122': 'LOC_Os01g02240',\n", " 'A123': 'LOC_Os01g02250',\n", " 'A124': 'LOC_Os01g02260',\n", " 'A125': 'LOC_Os01g02270',\n", " 'A126': 'LOC_Os01g02280',\n", " 'A127': 'LOC_Os01g02290',\n", " 'A128': 'LOC_Os01g02300',\n", " 'A129': 'LOC_Os01g02310',\n", " 'A130': 'LOC_Os01g02320',\n", " 'A131': 'LOC_Os01g02334',\n", " 'A132': 'LOC_Os01g02350',\n", " 'A133': 'LOC_Os01g02360',\n", " 'A134': 'LOC_Os01g02370',\n", " 'A135': 'LOC_Os01g02380',\n", " 'A136': 'LOC_Os01g02390',\n", " 'A137': 'LOC_Os01g02400',\n", " 'A138': 'LOC_Os01g02410',\n", " 'A139': 'LOC_Os01g02420',\n", " 'A140': 'LOC_Os01g02430',\n", " 'A141': 'LOC_Os01g02440',\n", " 'A142': 'LOC_Os01g02450',\n", " 'A143': 'LOC_Os01g02460',\n", " 'A144': 'LOC_Os01g02470',\n", " 'A145': 'LOC_Os01g02480',\n", " 'A146': 'LOC_Os01g02490',\n", " 'A147': 'LOC_Os01g02500',\n", " 'A148': 'LOC_Os01g02510',\n", " 'A149': 'LOC_Os01g02520',\n", " 'A150': 'LOC_Os01g02530',\n", " 'A151': 'LOC_Os01g02540',\n", " 'A152': 'LOC_Os01g02550',\n", " 'A153': 'LOC_Os01g02560',\n", " 'A154': 'LOC_Os01g02570',\n", " 'A155': 'LOC_Os01g02580',\n", " 'A156': 'LOC_Os01g02590',\n", " 'A157': 'LOC_Os01g02600',\n", " 'A158': 'LOC_Os01g02610',\n", " 'A159': 'LOC_Os01g02629',\n", " 'A160': 'LOC_Os01g02650',\n", " 'A161': 'LOC_Os01g02660',\n", " 'A162': 'LOC_Os01g02670',\n", " 'A163': 'LOC_Os01g02680',\n", " 'A164': 'LOC_Os01g02690',\n", " 'A165': 'LOC_Os01g02700',\n", " 'A166': 'LOC_Os01g02710',\n", " 'A167': 'LOC_Os01g02720',\n", " 'A168': 'LOC_Os01g02730',\n", " 'A169': 'LOC_Os01g02740',\n", " 'A170': 'LOC_Os01g02750',\n", " 'A171': 'LOC_Os01g02760',\n", " 'A172': 'LOC_Os01g02770',\n", " 'A173': 'LOC_Os01g02780',\n", " 'A174': 'LOC_Os01g02790',\n", " 'A175': 'LOC_Os01g02800',\n", " 'A176': 'LOC_Os01g02810',\n", " 'A177': 'LOC_Os01g02830',\n", " 'A178': 'LOC_Os01g02840',\n", " 'A179': 'LOC_Os01g02850',\n", " 'A180': 'LOC_Os01g02860',\n", " 'A181': 'LOC_Os01g02870',\n", " 'A182': 'LOC_Os01g02880',\n", " 'A183': 'LOC_Os01g02884',\n", " 'A184': 'LOC_Os01g02890',\n", " 'A185': 'LOC_Os01g02900',\n", " 'A186': 'LOC_Os01g02910',\n", " 'A187': 'LOC_Os01g02920',\n", " 'A188': 'LOC_Os01g02930',\n", " 'A189': 'LOC_Os01g02940',\n", " 'A190': 'LOC_Os01g02950',\n", " 'A191': 'LOC_Os01g02960',\n", " 'A192': 'LOC_Os01g02970',\n", " 'A193': 'LOC_Os01g02980',\n", " 'A194': 'LOC_Os01g02990',\n", " 'A195': 'LOC_Os01g03010',\n", " 'A196': 'LOC_Os01g03020',\n", " 'A197': 'LOC_Os01g03030',\n", " 'A198': 'LOC_Os01g03040',\n", " 'A199': 'LOC_Os01g03050',\n", " 'A200': 'LOC_Os01g03060',\n", " 'A201': 'LOC_Os01g03070',\n", " 'A202': 'LOC_Os01g03080',\n", " 'A203': 'LOC_Os01g03090',\n", " 'A204': 'LOC_Os01g03100',\n", " 'A205': 'LOC_Os01g03110',\n", " 'A206': 'LOC_Os01g03130',\n", " 'A207': 'LOC_Os01g03144',\n", " 'A208': 'LOC_Os01g03160',\n", " 'A209': 'LOC_Os01g03170',\n", " 'A210': 'LOC_Os01g03180',\n", " 'A211': 'LOC_Os01g03190',\n", " 'A212': 'LOC_Os01g03200',\n", " 'A213': 'LOC_Os01g03210',\n", " 'A214': 'LOC_Os01g03220',\n", " 'A215': 'LOC_Os01g03230',\n", " 'A216': 'LOC_Os01g03240',\n", " 'A217': 'LOC_Os01g03250',\n", " 'A218': 'LOC_Os01g03260',\n", " 'A219': 'LOC_Os01g03270',\n", " 'A220': 'LOC_Os01g03280',\n", " 'A221': 'LOC_Os01g03290',\n", " 'A222': 'LOC_Os01g03300',\n", " 'A223': 'LOC_Os01g03310',\n", " 'A224': 'LOC_Os01g03320',\n", " 'A225': 'LOC_Os01g03330',\n", " 'A226': 'LOC_Os01g03340',\n", " 'A227': 'LOC_Os01g03350',\n", " 'A228': 'LOC_Os01g03360',\n", " 'A229': 'LOC_Os01g03370',\n", " 'A230': 'LOC_Os01g03380',\n", " 'A231': 'LOC_Os01g03390',\n", " 'A232': 'LOC_Os01g03400',\n", " 'A233': 'LOC_Os01g03410',\n", " 'A234': 'LOC_Os01g03420',\n", " 'A235': 'LOC_Os01g03429',\n", " 'A236': 'LOC_Os01g03440',\n", " 'A237': 'LOC_Os01g03452',\n", " 'A238': 'LOC_Os01g03464',\n", " 'A239': 'LOC_Os01g03480',\n", " 'A240': 'LOC_Os01g03490',\n", " 'A241': 'LOC_Os01g03500',\n", " 'A242': 'LOC_Os01g03510',\n", " 'A243': 'LOC_Os01g03520',\n", " 'A244': 'LOC_Os01g03530',\n", " 'A245': 'LOC_Os01g03549',\n", " 'A246': 'LOC_Os01g03570',\n", " 'A247': 'LOC_Os01g03580',\n", " 'A248': 'LOC_Os01g03590',\n", " 'A249': 'LOC_Os01g03600',\n", " 'A250': 'LOC_Os01g03610',\n", " 'A251': 'LOC_Os01g03620',\n", " 'A252': 'LOC_Os01g03630',\n", " 'A253': 'LOC_Os01g03640',\n", " 'A254': 'LOC_Os01g03650',\n", " 'A255': 'LOC_Os01g03660',\n", " 'A256': 'LOC_Os01g03670',\n", " 'A257': 'LOC_Os01g03680',\n", " 'A258': 'LOC_Os01g03690',\n", " 'A259': 'LOC_Os01g03710',\n", " 'A260': 'LOC_Os01g03720',\n", " 'A261': 'LOC_Os01g03730',\n", " 'A262': 'LOC_Os01g03740',\n", " 'A263': 'LOC_Os01g03750',\n", " 'A264': 'LOC_Os01g03760',\n", " 'A265': 'LOC_Os01g03770',\n", " 'A266': 'LOC_Os01g03780',\n", " 'A267': 'LOC_Os01g03790',\n", " 'A268': 'LOC_Os01g03800',\n", " 'A269': 'LOC_Os01g03810',\n", " 'A270': 'LOC_Os01g03820',\n", " 'A271': 'LOC_Os01g03830',\n", " 'A272': 'LOC_Os01g03840',\n", " 'A273': 'LOC_Os01g03860',\n", " 'A274': 'LOC_Os01g03870',\n", " 'A275': 'LOC_Os01g03880',\n", " 'A276': 'LOC_Os01g03890',\n", " 'A277': 'LOC_Os01g03900',\n", " 'A278': 'LOC_Os01g03914',\n", " 'A279': 'LOC_Os01g03930',\n", " 'A280': 'LOC_Os01g03940',\n", " 'A281': 'LOC_Os01g03950',\n", " 'A282': 'LOC_Os01g03960',\n", " 'A283': 'LOC_Os01g03970',\n", " 'A284': 'LOC_Os01g03980',\n", " 'A285': 'LOC_Os01g03989',\n", " 'A286': 'LOC_Os01g04000',\n", " 'A287': 'LOC_Os01g04005',\n", " 'A288': 'LOC_Os01g04010',\n", " 'A289': 'LOC_Os01g04020',\n", " 'A290': 'LOC_Os01g04030',\n", " 'A291': 'LOC_Os01g04040',\n", " 'A292': 'LOC_Os01g04050',\n", " 'A293': 'LOC_Os01g04070',\n", " 'A294': 'LOC_Os01g04080',\n", " 'A295': 'LOC_Os01g04090',\n", " 'A296': 'LOC_Os01g04100',\n", " 'A297': 'LOC_Os01g04110',\n", " 'A298': 'LOC_Os01g04120',\n", " 'A299': 'LOC_Os01g04130',\n", " 'A300': 'LOC_Os01g04140',\n", " 'A301': 'LOC_Os01g04150',\n", " 'A302': 'LOC_Os01g04160',\n", " 'A303': 'LOC_Os01g04170',\n", " 'A304': 'LOC_Os01g04180',\n", " 'A305': 'LOC_Os01g04190',\n", " 'A306': 'LOC_Os01g04200',\n", " 'A307': 'LOC_Os01g04210',\n", " 'A308': 'LOC_Os01g04220',\n", " 'A309': 'LOC_Os01g04230',\n", " 'A310': 'LOC_Os01g04250',\n", " 'A311': 'LOC_Os01g04260',\n", " 'A312': 'LOC_Os01g04270',\n", " 'A313': 'LOC_Os01g04280',\n", " 'A314': 'LOC_Os01g04290',\n", " 'A315': 'LOC_Os01g04300',\n", " 'A316': 'LOC_Os01g04310',\n", " 'A317': 'LOC_Os01g04320',\n", " 'A318': 'LOC_Os01g04324',\n", " 'A319': 'LOC_Os01g04330',\n", " 'A320': 'LOC_Os01g04340',\n", " 'A321': 'LOC_Os01g04350',\n", " 'A322': 'LOC_Os01g04360',\n", " 'A323': 'LOC_Os01g04370',\n", " 'A324': 'LOC_Os01g04380',\n", " 'A325': 'LOC_Os01g04390',\n", " 'A326': 'LOC_Os01g04409',\n", " 'A327': 'LOC_Os01g04430',\n", " 'A328': 'LOC_Os01g04440',\n", " 'A329': 'LOC_Os01g04450',\n", " 'A330': 'LOC_Os01g04460',\n", " 'A331': 'LOC_Os01g04470',\n", " 'A332': 'LOC_Os01g04480',\n", " 'A333': 'LOC_Os01g04490',\n", " 'A334': 'LOC_Os01g04500',\n", " 'A335': 'LOC_Os01g04510',\n", " 'A336': 'LOC_Os01g04520',\n", " 'A337': 'LOC_Os01g04530',\n", " 'A338': 'LOC_Os01g04540',\n", " 'A339': 'LOC_Os01g04550',\n", " 'A340': 'LOC_Os01g04560',\n", " 'A341': 'LOC_Os01g04570',\n", " 'A342': 'LOC_Os01g04580',\n", " 'A343': 'LOC_Os01g04590',\n", " 'A344': 'LOC_Os01g04600',\n", " 'A345': 'LOC_Os01g04610',\n", " 'A346': 'LOC_Os01g04620',\n", " 'A347': 'LOC_Os01g04630',\n", " 'A348': 'LOC_Os01g04640',\n", " 'A349': 'LOC_Os01g04650',\n", " 'A350': 'LOC_Os01g04660',\n", " 'A351': 'LOC_Os01g04670',\n", " 'A352': 'LOC_Os01g04680',\n", " 'A353': 'LOC_Os01g04690',\n", " 'A354': 'LOC_Os01g04699',\n", " 'A355': 'LOC_Os01g04710',\n", " 'A356': 'LOC_Os01g04720',\n", " 'A357': 'LOC_Os01g04730',\n", " 'A358': 'LOC_Os01g04740',\n", " 'A359': 'LOC_Os01g04750',\n", " 'A360': 'LOC_Os01g04760',\n", " 'A361': 'LOC_Os01g04770',\n", " 'A362': 'LOC_Os01g04780',\n", " 'A363': 'LOC_Os01g04790',\n", " 'A364': 'LOC_Os01g04800',\n", " 'A365': 'LOC_Os01g04814',\n", " 'A366': 'LOC_Os01g04830',\n", " 'A367': 'LOC_Os01g04840',\n", " 'A368': 'LOC_Os01g04850',\n", " 'A369': 'LOC_Os01g04860',\n", " 'A370': 'LOC_Os01g04870',\n", " 'A371': 'LOC_Os01g04880',\n", " 'A372': 'LOC_Os01g04900',\n", " 'A373': 'LOC_Os01g04910',\n", " 'A374': 'LOC_Os01g04920',\n", " 'A375': 'LOC_Os01g04930',\n", " 'A376': 'LOC_Os01g04950',\n", " 'A377': 'LOC_Os01g04960',\n", " 'A378': 'LOC_Os01g04970',\n", " 'A379': 'LOC_Os01g04980',\n", " 'A380': 'LOC_Os01g04990',\n", " 'A381': 'LOC_Os01g05000',\n", " 'A382': 'LOC_Os01g05010',\n", " 'A383': 'LOC_Os01g05020',\n", " 'A384': 'LOC_Os01g05030',\n", " 'A385': 'LOC_Os01g05040',\n", " 'A386': 'LOC_Os01g05050',\n", " 'A387': 'LOC_Os01g05060',\n", " 'A388': 'LOC_Os01g05064',\n", " 'A389': 'LOC_Os01g05070',\n", " 'A390': 'LOC_Os01g05080',\n", " 'A391': 'LOC_Os01g05090',\n", " 'A392': 'LOC_Os01g05100',\n", " 'A393': 'LOC_Os01g05105',\n", " 'A394': 'LOC_Os01g05120',\n", " 'A395': 'LOC_Os01g05130',\n", " 'A396': 'LOC_Os01g05140',\n", " 'A397': 'LOC_Os01g05150',\n", " 'A398': 'LOC_Os01g05160',\n", " 'A399': 'LOC_Os01g05170',\n", " 'A400': 'LOC_Os01g05180',\n", " 'A401': 'LOC_Os01g05200',\n", " 'A402': 'LOC_Os01g05210',\n", " 'A403': 'LOC_Os01g05220',\n", " 'A404': 'LOC_Os01g05240',\n", " 'A405': 'LOC_Os01g05250',\n", " 'A406': 'LOC_Os01g05260',\n", " 'A407': 'LOC_Os01g05280',\n", " 'A408': 'LOC_Os01g05289',\n", " 'A409': 'LOC_Os01g05300',\n", " 'A410': 'LOC_Os01g05310',\n", " 'A411': 'LOC_Os01g05320',\n", " 'A412': 'LOC_Os01g05330',\n", " 'A413': 'LOC_Os01g05340',\n", " 'A414': 'LOC_Os01g05350',\n", " 'A415': 'LOC_Os01g05360',\n", " 'A416': 'LOC_Os01g05370',\n", " 'A417': 'LOC_Os01g05380',\n", " 'A418': 'LOC_Os01g05390',\n", " 'A419': 'LOC_Os01g05400',\n", " 'A420': 'LOC_Os01g05410',\n", " 'A421': 'LOC_Os01g05420',\n", " 'A422': 'LOC_Os01g05430',\n", " 'A423': 'LOC_Os01g05440',\n", " 'A424': 'LOC_Os01g05450',\n", " 'A425': 'LOC_Os01g05460',\n", " 'A426': 'LOC_Os01g05470',\n", " 'A427': 'LOC_Os01g05480',\n", " 'A428': 'LOC_Os01g05490',\n", " 'A429': 'LOC_Os01g05500',\n", " 'A430': 'LOC_Os01g05510',\n", " 'A431': 'LOC_Os01g05520',\n", " 'A432': 'LOC_Os01g05530',\n", " 'A433': 'LOC_Os01g05540',\n", " 'A434': 'LOC_Os01g05550',\n", " 'A435': 'LOC_Os01g05560',\n", " 'A436': 'LOC_Os01g05570',\n", " 'A437': 'LOC_Os01g05580',\n", " 'A438': 'LOC_Os01g05585',\n", " 'A439': 'LOC_Os01g05590',\n", " 'A440': 'LOC_Os01g05600',\n", " 'A441': 'LOC_Os01g05610',\n", " 'A442': 'LOC_Os01g05620',\n", " 'A443': 'LOC_Os01g05630',\n", " 'A444': 'LOC_Os01g05640',\n", " 'A445': 'LOC_Os01g05650',\n", " 'A446': 'LOC_Os01g05660',\n", " 'A447': 'LOC_Os01g05670',\n", " 'A448': 'LOC_Os01g05680',\n", " 'A449': 'LOC_Os01g05694',\n", " 'A450': 'LOC_Os01g05710',\n", " 'A451': 'LOC_Os01g05720',\n", " 'A452': 'LOC_Os01g05730',\n", " 'A453': 'LOC_Os01g05744',\n", " 'A454': 'LOC_Os01g05760',\n", " 'A455': 'LOC_Os01g05770',\n", " 'A456': 'LOC_Os01g05780',\n", " 'A457': 'LOC_Os01g05790',\n", " 'A458': 'LOC_Os01g05800',\n", " 'A459': 'LOC_Os01g05810',\n", " 'A460': 'LOC_Os01g05820',\n", " 'A461': 'LOC_Os01g05830',\n", " 'A462': 'LOC_Os01g05840',\n", " 'A463': 'LOC_Os01g05850',\n", " 'A464': 'LOC_Os01g05860',\n", " 'A465': 'LOC_Os01g05870',\n", " 'A466': 'LOC_Os01g05880',\n", " 'A467': 'LOC_Os01g05890',\n", " 'A468': 'LOC_Os01g05900',\n", " 'A469': 'LOC_Os01g05940',\n", " 'A470': 'LOC_Os01g05960',\n", " 'A471': 'LOC_Os01g05970',\n", " 'A472': 'LOC_Os01g05980',\n", " 'A473': 'LOC_Os01g06000',\n", " 'A474': 'LOC_Os01g06010',\n", " 'A475': 'LOC_Os01g06020',\n", " 'A476': 'LOC_Os01g06030',\n", " 'A477': 'LOC_Os01g06040',\n", " 'A478': 'LOC_Os01g06050',\n", " 'A479': 'LOC_Os01g06060',\n", " 'A480': 'LOC_Os01g06070',\n", " 'A481': 'LOC_Os01g06080',\n", " 'A482': 'LOC_Os01g06090',\n", " 'A483': 'LOC_Os01g06100',\n", " 'A484': 'LOC_Os01g06110',\n", " 'A485': 'LOC_Os01g06120',\n", " 'A486': 'LOC_Os01g06130',\n", " 'A487': 'LOC_Os01g06140',\n", " 'A488': 'LOC_Os01g06150',\n", " 'A489': 'LOC_Os01g06160',\n", " 'A490': 'LOC_Os01g06170',\n", " 'A491': 'LOC_Os01g06180',\n", " 'A492': 'LOC_Os01g06200',\n", " 'A493': 'LOC_Os01g06210',\n", " 'A494': 'LOC_Os01g06220',\n", " 'A495': 'LOC_Os01g06230',\n", " 'A496': 'LOC_Os01g06240',\n", " 'A497': 'LOC_Os01g06250',\n", " 'A498': 'LOC_Os01g06260',\n", " 'A499': 'LOC_Os01g06270',\n", " 'A500': 'LOC_Os01g06280',\n", " 'A501': 'LOC_Os01g06290',\n", " 'A502': 'LOC_Os01g06300',\n", " 'A503': 'LOC_Os01g06310',\n", " 'A504': 'LOC_Os01g06320',\n", " 'A505': 'LOC_Os01g06330',\n", " 'A506': 'LOC_Os01g06340',\n", " 'A507': 'LOC_Os01g06350',\n", " 'A508': 'LOC_Os01g06360',\n", " 'A509': 'LOC_Os01g06370',\n", " 'A510': 'LOC_Os01g06380',\n", " 'A511': 'LOC_Os01g06390',\n", " 'A512': 'LOC_Os01g06400',\n", " 'A513': 'LOC_Os01g06410',\n", " 'A514': 'LOC_Os01g06420',\n", " 'A515': 'LOC_Os01g06430',\n", " 'A516': 'LOC_Os01g06440',\n", " 'A517': 'LOC_Os01g06450',\n", " 'A518': 'LOC_Os01g06454',\n", " 'A519': 'LOC_Os01g06460',\n", " 'A520': 'LOC_Os01g06470',\n", " 'A521': 'LOC_Os01g06490',\n", " 'A522': 'LOC_Os01g06500',\n", " 'A523': 'LOC_Os01g06510',\n", " 'A524': 'LOC_Os01g06520',\n", " 'A525': 'LOC_Os01g06530',\n", " 'A526': 'LOC_Os01g06540',\n", " 'A527': 'LOC_Os01g06550',\n", " 'A528': 'LOC_Os01g06560',\n", " 'A529': 'LOC_Os01g06570',\n", " 'A530': 'LOC_Os01g06580',\n", " 'A531': 'LOC_Os01g06590',\n", " 'A532': 'LOC_Os01g06600',\n", " 'A533': 'LOC_Os01g06610',\n", " 'A534': 'LOC_Os01g06620',\n", " 'A535': 'LOC_Os01g06630',\n", " 'A536': 'LOC_Os01g06640',\n", " 'A537': 'LOC_Os01g06650',\n", " 'A538': 'LOC_Os01g06660',\n", " 'A539': 'LOC_Os01g06670',\n", " 'A540': 'LOC_Os01g06680',\n", " 'A541': 'LOC_Os01g06690',\n", " 'A542': 'LOC_Os01g06700',\n", " 'A543': 'LOC_Os01g06710',\n", " 'A544': 'LOC_Os01g06720',\n", " 'A545': 'LOC_Os01g06730',\n", " 'A546': 'LOC_Os01g06740',\n", " 'A547': 'LOC_Os01g06750',\n", " 'A548': 'LOC_Os01g06760',\n", " 'A549': 'LOC_Os01g06770',\n", " 'A550': 'LOC_Os01g06780',\n", " 'A551': 'LOC_Os01g06790',\n", " 'A552': 'LOC_Os01g06800',\n", " 'A553': 'LOC_Os01g06810',\n", " 'A554': 'LOC_Os01g06820',\n", " 'A555': 'LOC_Os01g06836',\n", " 'A556': 'LOC_Os01g06852',\n", " 'A557': 'LOC_Os01g06870',\n", " 'A558': 'LOC_Os01g06876',\n", " 'A559': 'LOC_Os01g06882',\n", " 'A560': 'LOC_Os01g06890',\n", " 'A561': 'LOC_Os01g06900',\n", " 'A562': 'LOC_Os01g06910',\n", " 'A563': 'LOC_Os01g06920',\n", " 'A564': 'LOC_Os01g06940',\n", " 'A565': 'LOC_Os01g06950',\n", " 'A566': 'LOC_Os01g06960',\n", " 'A567': 'LOC_Os01g06970',\n", " 'A568': 'LOC_Os01g06980',\n", " 'A569': 'LOC_Os01g06990',\n", " 'A570': 'LOC_Os01g07000',\n", " 'A571': 'LOC_Os01g07010',\n", " 'A572': 'LOC_Os01g07020',\n", " 'A573': 'LOC_Os01g07030',\n", " 'A574': 'LOC_Os01g07040',\n", " 'A575': 'LOC_Os01g07050',\n", " 'A576': 'LOC_Os01g07060',\n", " 'A577': 'LOC_Os01g07070',\n", " 'A578': 'LOC_Os01g07080',\n", " 'A579': 'LOC_Os01g07090',\n", " 'A580': 'LOC_Os01g07100',\n", " 'A581': 'LOC_Os01g07110',\n", " 'A582': 'LOC_Os01g07120',\n", " 'A583': 'LOC_Os01g07130',\n", " 'A584': 'LOC_Os01g07140',\n", " 'A585': 'LOC_Os01g07150',\n", " 'A586': 'LOC_Os01g07160',\n", " 'A587': 'LOC_Os01g07170',\n", " 'A588': 'LOC_Os01g07180',\n", " 'A589': 'LOC_Os01g07190',\n", " 'A590': 'LOC_Os01g07200',\n", " 'A591': 'LOC_Os01g07212',\n", " 'A592': 'LOC_Os01g07240',\n", " 'A593': 'LOC_Os01g07250',\n", " 'A594': 'LOC_Os01g07260',\n", " 'A595': 'LOC_Os01g07270',\n", " 'A596': 'LOC_Os01g07280',\n", " 'A597': 'LOC_Os01g07300',\n", " 'A598': 'LOC_Os01g07310',\n", " 'A599': 'LOC_Os01g07320',\n", " 'A600': 'LOC_Os01g07330',\n", " 'A601': 'LOC_Os01g07340',\n", " 'A602': 'LOC_Os01g07350',\n", " 'A603': 'LOC_Os01g07360',\n", " 'A604': 'LOC_Os01g07364',\n", " 'A605': 'LOC_Os01g07370',\n", " 'A606': 'LOC_Os01g07376',\n", " 'A607': 'LOC_Os01g07382',\n", " 'A608': 'LOC_Os01g07390',\n", " 'A609': 'LOC_Os01g07400',\n", " 'A610': 'LOC_Os01g07410',\n", " 'A611': 'LOC_Os01g07420',\n", " 'A612': 'LOC_Os01g07430',\n", " 'A613': 'LOC_Os01g07450',\n", " 'A614': 'LOC_Os01g07460',\n", " 'A615': 'LOC_Os01g07470',\n", " 'A616': 'LOC_Os01g07480',\n", " 'A617': 'LOC_Os01g07490',\n", " 'A618': 'LOC_Os01g07500',\n", " 'A619': 'LOC_Os01g07510',\n", " 'A620': 'LOC_Os01g07520',\n", " 'A621': 'LOC_Os01g07530',\n", " 'A622': 'LOC_Os01g07540',\n", " 'A623': 'LOC_Os01g07550',\n", " 'A624': 'LOC_Os01g07560',\n", " 'A625': 'LOC_Os01g07570',\n", " 'A626': 'LOC_Os01g07590',\n", " 'A627': 'LOC_Os01g07600',\n", " 'A628': 'LOC_Os01g07610',\n", " 'A629': 'LOC_Os01g07620',\n", " 'A630': 'LOC_Os01g07630',\n", " 'A631': 'LOC_Os01g07640',\n", " 'A632': 'LOC_Os01g07650',\n", " 'A633': 'LOC_Os01g07660',\n", " 'A634': 'LOC_Os01g07670',\n", " 'A635': 'LOC_Os01g07680',\n", " 'A636': 'LOC_Os01g07700',\n", " 'A637': 'LOC_Os01g07710',\n", " 'A638': 'LOC_Os01g07720',\n", " 'A639': 'LOC_Os01g07730',\n", " 'A640': 'LOC_Os01g07740',\n", " 'A641': 'LOC_Os01g07750',\n", " 'A642': 'LOC_Os01g07760',\n", " 'A643': 'LOC_Os01g07770',\n", " 'A644': 'LOC_Os01g07780',\n", " 'A645': 'LOC_Os01g07790',\n", " 'A646': 'LOC_Os01g07800',\n", " 'A647': 'LOC_Os01g07810',\n", " 'A648': 'LOC_Os01g07820',\n", " 'A649': 'LOC_Os01g07830',\n", " 'A650': 'LOC_Os01g07840',\n", " 'A651': 'LOC_Os01g07850',\n", " 'A652': 'LOC_Os01g07860',\n", " 'A653': 'LOC_Os01g07870',\n", " 'A654': 'LOC_Os01g07880',\n", " 'A655': 'LOC_Os01g07890',\n", " 'A656': 'LOC_Os01g07900',\n", " 'A657': 'LOC_Os01g07910',\n", " 'A658': 'LOC_Os01g07920',\n", " 'A659': 'LOC_Os01g07930',\n", " 'A660': 'LOC_Os01g07940',\n", " 'A661': 'LOC_Os01g07950',\n", " 'A662': 'LOC_Os01g07960',\n", " 'A663': 'LOC_Os01g07970',\n", " 'A664': 'LOC_Os01g07980',\n", " 'A665': 'LOC_Os01g07990',\n", " 'A666': 'LOC_Os01g08000',\n", " 'A667': 'LOC_Os01g08010',\n", " 'A668': 'LOC_Os01g08020',\n", " 'A669': 'LOC_Os01g08050',\n", " 'A670': 'LOC_Os01g08060',\n", " 'A671': 'LOC_Os01g08070',\n", " 'A672': 'LOC_Os01g08090',\n", " 'A673': 'LOC_Os01g08100',\n", " 'A674': 'LOC_Os01g08110',\n", " 'A675': 'LOC_Os01g08120',\n", " 'A676': 'LOC_Os01g08130',\n", " 'A677': 'LOC_Os01g08140',\n", " 'A678': 'LOC_Os01g08150',\n", " 'A679': 'LOC_Os01g08160',\n", " 'A680': 'LOC_Os01g08170',\n", " 'A681': 'LOC_Os01g08180',\n", " 'A682': 'LOC_Os01g08190',\n", " 'A683': 'LOC_Os01g08200',\n", " 'A684': 'LOC_Os01g08220',\n", " 'A685': 'LOC_Os01g08240',\n", " 'A686': 'LOC_Os01g08250',\n", " 'A687': 'LOC_Os01g08260',\n", " 'A688': 'LOC_Os01g08270',\n", " 'A689': 'LOC_Os01g08280',\n", " 'A690': 'LOC_Os01g08290',\n", " 'A691': 'LOC_Os01g08300',\n", " 'A692': 'LOC_Os01g08310',\n", " 'A693': 'LOC_Os01g08320',\n", " 'A694': 'LOC_Os01g08330',\n", " 'A695': 'LOC_Os01g08340',\n", " 'A696': 'LOC_Os01g08350',\n", " 'A697': 'LOC_Os01g08360',\n", " 'A698': 'LOC_Os01g08370',\n", " 'A699': 'LOC_Os01g08380',\n", " 'A700': 'LOC_Os01g08390',\n", " 'A701': 'LOC_Os01g08400',\n", " 'A702': 'LOC_Os01g08410',\n", " 'A703': 'LOC_Os01g08420',\n", " 'A704': 'LOC_Os01g08430',\n", " 'A705': 'LOC_Os01g08440',\n", " 'A706': 'LOC_Os01g08450',\n", " 'A707': 'LOC_Os01g08460',\n", " 'A708': 'LOC_Os01g08464',\n", " 'A709': 'LOC_Os01g08470',\n", " 'A710': 'LOC_Os01g08480',\n", " 'A711': 'LOC_Os01g08490',\n", " 'A712': 'LOC_Os01g08500',\n", " 'A713': 'LOC_Os01g08510',\n", " 'A714': 'LOC_Os01g08520',\n", " 'A715': 'LOC_Os01g08530',\n", " 'A716': 'LOC_Os01g08540',\n", " 'A717': 'LOC_Os01g08550',\n", " 'A718': 'LOC_Os01g08560',\n", " 'A719': 'LOC_Os01g08570',\n", " 'A720': 'LOC_Os01g08580',\n", " 'A721': 'LOC_Os01g08590',\n", " 'A722': 'LOC_Os01g08600',\n", " 'A723': 'LOC_Os01g08610',\n", " 'A724': 'LOC_Os01g08620',\n", " 'A725': 'LOC_Os01g08630',\n", " 'A726': 'LOC_Os01g08640',\n", " 'A727': 'LOC_Os01g08650',\n", " 'A728': 'LOC_Os01g08660',\n", " 'A729': 'LOC_Os01g08670',\n", " 'A730': 'LOC_Os01g08680',\n", " 'A731': 'LOC_Os01g08690',\n", " 'A732': 'LOC_Os01g08700',\n", " 'A733': 'LOC_Os01g08710',\n", " 'A734': 'LOC_Os01g08720',\n", " 'A735': 'LOC_Os01g08730',\n", " 'A736': 'LOC_Os01g08740',\n", " 'A737': 'LOC_Os01g08750',\n", " 'A738': 'LOC_Os01g08760',\n", " 'A739': 'LOC_Os01g08770',\n", " 'A740': 'LOC_Os01g08780',\n", " 'A741': 'LOC_Os01g08790',\n", " 'A742': 'LOC_Os01g08800',\n", " 'A743': 'LOC_Os01g08810',\n", " 'A744': 'LOC_Os01g08814',\n", " 'A745': 'LOC_Os01g08820',\n", " 'A746': 'LOC_Os01g08830',\n", " 'A747': 'LOC_Os01g08840',\n", " 'A748': 'LOC_Os01g08850',\n", " 'A749': 'LOC_Os01g08860',\n", " 'A750': 'LOC_Os01g08870',\n", " 'A751': 'LOC_Os01g08880',\n", " 'A752': 'LOC_Os01g08890',\n", " 'A753': 'LOC_Os01g08930',\n", " 'A754': 'LOC_Os01g08940',\n", " 'A755': 'LOC_Os01g08950',\n", " 'A756': 'LOC_Os01g08960',\n", " 'A757': 'LOC_Os01g08970',\n", " 'A758': 'LOC_Os01g08980',\n", " 'A759': 'LOC_Os01g08990',\n", " 'A760': 'LOC_Os01g09000',\n", " 'A761': 'LOC_Os01g09010',\n", " 'A762': 'LOC_Os01g09020',\n", " 'A763': 'LOC_Os01g09030',\n", " 'A764': 'LOC_Os01g09050',\n", " 'A765': 'LOC_Os01g09060',\n", " 'A766': 'LOC_Os01g09080',\n", " 'A767': 'LOC_Os01g09090',\n", " 'A768': 'LOC_Os01g09100',\n", " 'A769': 'LOC_Os01g09110',\n", " 'A770': 'LOC_Os01g09120',\n", " 'A771': 'LOC_Os01g09130',\n", " 'A772': 'LOC_Os01g09140',\n", " 'A773': 'LOC_Os01g09150',\n", " 'A774': 'LOC_Os01g09160',\n", " 'A775': 'LOC_Os01g09170',\n", " 'A776': 'LOC_Os01g09180',\n", " 'A777': 'LOC_Os01g09190',\n", " 'A778': 'LOC_Os01g09200',\n", " 'A779': 'LOC_Os01g09206',\n", " 'A780': 'LOC_Os01g09212',\n", " 'A781': 'LOC_Os01g09220',\n", " 'A782': 'LOC_Os01g09230',\n", " 'A783': 'LOC_Os01g09240',\n", " 'A784': 'LOC_Os01g09246',\n", " 'A785': 'LOC_Os01g09252',\n", " 'A786': 'LOC_Os01g09260',\n", " 'A787': 'LOC_Os01g09270',\n", " 'A788': 'LOC_Os01g09280',\n", " 'A789': 'LOC_Os01g09290',\n", " 'A790': 'LOC_Os01g09300',\n", " 'A791': 'LOC_Os01g09305',\n", " 'A792': 'LOC_Os01g09310',\n", " 'A793': 'LOC_Os01g09320',\n", " 'A794': 'LOC_Os01g09330',\n", " 'A795': 'LOC_Os01g09340',\n", " 'A796': 'LOC_Os01g09350',\n", " 'A797': 'LOC_Os01g09360',\n", " 'A798': 'LOC_Os01g09370',\n", " 'A799': 'LOC_Os01g09384',\n", " 'A800': 'LOC_Os01g09400',\n", " 'A801': 'LOC_Os01g09410',\n", " 'A802': 'LOC_Os01g09420',\n", " 'A803': 'LOC_Os01g09430',\n", " 'A804': 'LOC_Os01g09440',\n", " 'A805': 'LOC_Os01g09450',\n", " 'A806': 'LOC_Os01g09460',\n", " 'A807': 'LOC_Os01g09470',\n", " 'A808': 'LOC_Os01g09480',\n", " 'A809': 'LOC_Os01g09490',\n", " 'A810': 'LOC_Os01g09500',\n", " 'A811': 'LOC_Os01g09510',\n", " 'A812': 'LOC_Os01g09520',\n", " 'A813': 'LOC_Os01g09530',\n", " 'A814': 'LOC_Os01g09540',\n", " 'A815': 'LOC_Os01g09550',\n", " 'A816': 'LOC_Os01g09560',\n", " 'A817': 'LOC_Os01g09570',\n", " 'A818': 'LOC_Os01g09580',\n", " 'A819': 'LOC_Os01g09590',\n", " 'A820': 'LOC_Os01g09600',\n", " 'A821': 'LOC_Os01g09610',\n", " 'A822': 'LOC_Os01g09620',\n", " 'A823': 'LOC_Os01g09640',\n", " 'A824': 'LOC_Os01g09650',\n", " 'A825': 'LOC_Os01g09660',\n", " 'A826': 'LOC_Os01g09665',\n", " 'A827': 'LOC_Os01g09670',\n", " 'A828': 'LOC_Os01g09680',\n", " 'A829': 'LOC_Os01g09690',\n", " 'A830': 'LOC_Os01g09700',\n", " 'A831': 'LOC_Os01g09710',\n", " 'A832': 'LOC_Os01g09720',\n", " 'A833': 'LOC_Os01g09730',\n", " 'A834': 'LOC_Os01g09740',\n", " 'A835': 'LOC_Os01g09750',\n", " 'A836': 'LOC_Os01g09760',\n", " 'A837': 'LOC_Os01g09770',\n", " 'A838': 'LOC_Os01g09790',\n", " 'A839': 'LOC_Os01g09800',\n", " 'A840': 'LOC_Os01g09810',\n", " 'A841': 'LOC_Os01g09830',\n", " 'A842': 'LOC_Os01g09850',\n", " 'A843': 'LOC_Os01g09860',\n", " 'A844': 'LOC_Os01g09870',\n", " 'A845': 'LOC_Os01g09880',\n", " 'A846': 'LOC_Os01g09890',\n", " 'A847': 'LOC_Os01g09900',\n", " 'A848': 'LOC_Os01g09930',\n", " 'A849': 'LOC_Os01g09940',\n", " 'A850': 'LOC_Os01g09950',\n", " 'A851': 'LOC_Os01g09960',\n", " 'A852': 'LOC_Os01g09970',\n", " 'A853': 'LOC_Os01g09980',\n", " 'A854': 'LOC_Os01g09990',\n", " 'A855': 'LOC_Os01g10000',\n", " 'A856': 'LOC_Os01g10010',\n", " 'A857': 'LOC_Os01g10020',\n", " 'A858': 'LOC_Os01g10030',\n", " 'A859': 'LOC_Os01g10040',\n", " 'A860': 'LOC_Os01g10050',\n", " 'A861': 'LOC_Os01g10060',\n", " 'A862': 'LOC_Os01g10070',\n", " 'A863': 'LOC_Os01g10080',\n", " 'A864': 'LOC_Os01g10090',\n", " 'A865': 'LOC_Os01g10100',\n", " 'A866': 'LOC_Os01g10110',\n", " 'A867': 'LOC_Os01g10120',\n", " 'A868': 'LOC_Os01g10130',\n", " 'A869': 'LOC_Os01g10140',\n", " 'A870': 'LOC_Os01g10150',\n", " 'A871': 'LOC_Os01g10160',\n", " 'A872': 'LOC_Os01g10170',\n", " 'A873': 'LOC_Os01g10180',\n", " 'A874': 'LOC_Os01g10195',\n", " 'A875': 'LOC_Os01g10200',\n", " 'A876': 'LOC_Os01g10210',\n", " 'A877': 'LOC_Os01g10230',\n", " 'A878': 'LOC_Os01g10240',\n", " 'A879': 'LOC_Os01g10250',\n", " 'A880': 'LOC_Os01g10260',\n", " 'A881': 'LOC_Os01g10270',\n", " 'A882': 'LOC_Os01g10280',\n", " 'A883': 'LOC_Os01g10290',\n", " 'A884': 'LOC_Os01g10300',\n", " 'A885': 'LOC_Os01g10310',\n", " 'A886': 'LOC_Os01g10320',\n", " 'A887': 'LOC_Os01g10340',\n", " 'A888': 'LOC_Os01g10350',\n", " 'A889': 'LOC_Os01g10370',\n", " 'A890': 'LOC_Os01g10380',\n", " 'A891': 'LOC_Os01g10390',\n", " 'A892': 'LOC_Os01g10400',\n", " 'A893': 'LOC_Os01g10410',\n", " 'A894': 'LOC_Os01g10420',\n", " 'A895': 'LOC_Os01g10430',\n", " 'A896': 'LOC_Os01g10440',\n", " 'A897': 'LOC_Os01g10450',\n", " 'A898': 'LOC_Os01g10460',\n", " 'A899': 'LOC_Os01g10470',\n", " 'A900': 'LOC_Os01g10480',\n", " 'A901': 'LOC_Os01g10490',\n", " 'A902': 'LOC_Os01g10504',\n", " 'A903': 'LOC_Os01g10520',\n", " 'A904': 'LOC_Os01g10530',\n", " 'A905': 'LOC_Os01g10550',\n", " 'A906': 'LOC_Os01g10560',\n", " 'A907': 'LOC_Os01g10580',\n", " 'A908': 'LOC_Os01g10590',\n", " 'A909': 'LOC_Os01g10600',\n", " 'A910': 'LOC_Os01g10610',\n", " 'A911': 'LOC_Os01g10630',\n", " 'A912': 'LOC_Os01g10640',\n", " 'A913': 'LOC_Os01g10650',\n", " 'A914': 'LOC_Os01g10660',\n", " 'A915': 'LOC_Os01g10670',\n", " 'A916': 'LOC_Os01g10680',\n", " 'A917': 'LOC_Os01g10690',\n", " 'A918': 'LOC_Os01g10700',\n", " 'A919': 'LOC_Os01g10710',\n", " 'A920': 'LOC_Os01g10720',\n", " 'A921': 'LOC_Os01g10740',\n", " 'A922': 'LOC_Os01g10750',\n", " 'A923': 'LOC_Os01g10760',\n", " 'A924': 'LOC_Os01g10770',\n", " 'A925': 'LOC_Os01g10780',\n", " 'A926': 'LOC_Os01g10790',\n", " 'A927': 'LOC_Os01g10800',\n", " 'A928': 'LOC_Os01g10810',\n", " 'A929': 'LOC_Os01g10820',\n", " 'A930': 'LOC_Os01g10830',\n", " 'A931': 'LOC_Os01g10840',\n", " 'A932': 'LOC_Os01g10850',\n", " 'A933': 'LOC_Os01g10860',\n", " 'A934': 'LOC_Os01g10870',\n", " 'A935': 'LOC_Os01g10880',\n", " 'A936': 'LOC_Os01g10890',\n", " 'A937': 'LOC_Os01g10900',\n", " 'A938': 'LOC_Os01g10910',\n", " 'A939': 'LOC_Os01g10920',\n", " 'A940': 'LOC_Os01g10930',\n", " 'A941': 'LOC_Os01g10940',\n", " 'A942': 'LOC_Os01g10950',\n", " 'A943': 'LOC_Os01g10960',\n", " 'A944': 'LOC_Os01g10970',\n", " 'A945': 'LOC_Os01g10980',\n", " 'A946': 'LOC_Os01g10990',\n", " 'A947': 'LOC_Os01g11000',\n", " 'A948': 'LOC_Os01g11010',\n", " 'A949': 'LOC_Os01g11020',\n", " 'A950': 'LOC_Os01g11040',\n", " 'A951': 'LOC_Os01g11054',\n", " 'A952': 'LOC_Os01g11070',\n", " 'A953': 'LOC_Os01g11080',\n", " 'A954': 'LOC_Os01g11110',\n", " 'A955': 'LOC_Os01g11120',\n", " 'A956': 'LOC_Os01g11130',\n", " 'A957': 'LOC_Os01g11140',\n", " 'A958': 'LOC_Os01g11150',\n", " 'A959': 'LOC_Os01g11160',\n", " 'A960': 'LOC_Os01g11170',\n", " 'A961': 'LOC_Os01g11190',\n", " 'A962': 'LOC_Os01g11200',\n", " 'A963': 'LOC_Os01g11210',\n", " 'A964': 'LOC_Os01g11220',\n", " 'A965': 'LOC_Os01g11230',\n", " 'A966': 'LOC_Os01g11240',\n", " 'A967': 'LOC_Os01g11250',\n", " 'A968': 'LOC_Os01g11260',\n", " 'A969': 'LOC_Os01g11270',\n", " 'A970': 'LOC_Os01g11280',\n", " 'A971': 'LOC_Os01g11300',\n", " 'A972': 'LOC_Os01g11310',\n", " 'A973': 'LOC_Os01g11320',\n", " 'A974': 'LOC_Os01g11330',\n", " 'A975': 'LOC_Os01g11340',\n", " 'A976': 'LOC_Os01g11350',\n", " 'A977': 'LOC_Os01g11360',\n", " 'A978': 'LOC_Os01g11370',\n", " 'A979': 'LOC_Os01g11390',\n", " 'A980': 'LOC_Os01g11400',\n", " 'A981': 'LOC_Os01g11414',\n", " 'A982': 'LOC_Os01g11430',\n", " 'A983': 'LOC_Os01g11450',\n", " 'A984': 'LOC_Os01g11460',\n", " 'A985': 'LOC_Os01g11470',\n", " 'A986': 'LOC_Os01g11480',\n", " 'A987': 'LOC_Os01g11490',\n", " 'A988': 'LOC_Os01g11500',\n", " 'A989': 'LOC_Os01g11510',\n", " 'A990': 'LOC_Os01g11520',\n", " 'A991': 'LOC_Os01g11550',\n", " 'A992': 'LOC_Os01g11570',\n", " 'A993': 'LOC_Os01g11580',\n", " 'A994': 'LOC_Os01g11590',\n", " 'A995': 'LOC_Os01g11600',\n", " 'A996': 'LOC_Os01g11620',\n", " 'A997': 'LOC_Os01g11640',\n", " 'A998': 'LOC_Os01g11650',\n", " 'A999': 'LOC_Os01g11660',\n", " 'A1000': 'LOC_Os01g11670',\n", " ...}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["fourth_round[\"locus_id\"].to_dict()"]}, {"cell_type": "code", "execution_count": null, "id": "a619c6e1", "metadata": {}, "outputs": [], "source": ["id_map.to_dict()"]}, {"cell_type": "code", "execution_count": 8, "id": "5dd89521", "metadata": {}, "outputs": [], "source": ["plan = pd.read_excel(\"/Users/<USER>/Downloads/20250517 第五轮载体构建 任务规划V1.xlsx\", sheet_name=\"第五轮拟构建载体（去重后）\")"]}, {"cell_type": "code", "execution_count": 29, "id": "ddba0761", "metadata": {}, "outputs": [], "source": ["plan[\"locus_id\"] = plan[\"基因代码\"].map(id_map.to_dict())\n"]}, {"cell_type": "code", "execution_count": 23, "id": "b04eb9e0", "metadata": {}, "outputs": [], "source": ["pairs = pd.read_excel(\"/Users/<USER>/Desktop/pairs.xlsx\")\n", "pairs.query(\"locus_id in @selected\").to_excel(\"/Users/<USER>/Desktop/pairs_selected.xlsx\")"]}, {"cell_type": "code", "execution_count": 36, "id": "0a5031cd", "metadata": {}, "outputs": [], "source": ["pairs.merge(plan, how=\"right\", on=\"locus_id\").to_excel(\"/Users/<USER>/Desktop/pairs_selected.xlsx\")"]}, {"cell_type": "code", "execution_count": 38, "id": "fba54d93", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "pd.concat([pd.read_parquet(p) for p in Path(\".\").glob(\"*.parquet\")]).to_excel(\"/Users/<USER>/Desktop/off_targets.xlsx\")"]}, {"cell_type": "code", "execution_count": 5, "id": "5fae2b7a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Locus_id</th>\n", "      <th>Note</th>\n", "      <th>Transcript_evidence</th>\n", "      <th>ORF_evidence</th>\n", "      <th>InterPro</th>\n", "      <th>Parent</th>\n", "      <th>GO</th>\n", "      <th>B5toI1</th>\n", "      <th>Oryzabase Gene Symbol Synonym(s)</th>\n", "      <th>Oryzabase Gene Name Synonym(s)</th>\n", "      <th>Oryzabase</th>\n", "      <th>RAP-DB Gene Symbol Synonym(s)</th>\n", "      <th>RAP-DB Gene Name Synonym(s)</th>\n", "      <th>Manual Curation</th>\n", "      <th>Literature_PMID</th>\n", "      <th>CGSNL Gene Symbol</th>\n", "      <th>CGSNL Gene Name</th>\n", "      <th>Subcellular location</th>\n", "      <th>Comment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>243597</th>\n", "      <td>chr08</td>\n", "      <td>irgsp1_rep</td>\n", "      <td>mRNA</td>\n", "      <td>21307600</td>\n", "      <td>21309069</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>Os08t0438400-01</td>\n", "      <td>Os08t0438400-01</td>\n", "      <td>Os08g0438400</td>\n", "      <td>Similar to ZF-HD homeobox protein.</td>\n", "      <td>AK288007</td>\n", "      <td>NP_001149634.1 (RefSeq)</td>\n", "      <td>Homeodomain, ZF-HD class (IPR006455),ZF-HD hom...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seqid      source  type  ...  CGSNL Gene Name  Subcellular location  Comment\n", "243597  chr08  irgsp1_rep  mRNA  ...              NaN                   NaN      NaN\n", "\n", "[1 rows x 29 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_gff3\n", "\n", "gff = read_gff3(\n", "        \"tar://IRGSP-1.0_representative/transcripts.gff::\"\n", "        \"https://rapdb.dna.affrc.go.jp/download/archive/irgsp1/\"\n", "        \"IRGSP-1.0_representative_2025-03-19.tar.gz\"\n", "    )\n", "gff.query(\"type == 'mRNA' and Locus_id == 'Os08g0438400'\")"]}, {"cell_type": "code", "execution_count": 7, "id": "662e1c5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Locus_id</th>\n", "      <th>Note</th>\n", "      <th>Transcript_evidence</th>\n", "      <th>ORF_evidence</th>\n", "      <th>InterPro</th>\n", "      <th>Parent</th>\n", "      <th>GO</th>\n", "      <th>B5toI1</th>\n", "      <th>Oryzabase Gene Symbol Synonym(s)</th>\n", "      <th>Oryzabase Gene Name Synonym(s)</th>\n", "      <th>Oryzabase</th>\n", "      <th>RAP-DB Gene Symbol Synonym(s)</th>\n", "      <th>RAP-DB Gene Name Synonym(s)</th>\n", "      <th>Manual Curation</th>\n", "      <th>Literature_PMID</th>\n", "      <th>CGSNL Gene Symbol</th>\n", "      <th>CGSNL Gene Name</th>\n", "      <th>Subcellular location</th>\n", "      <th>Comment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>243599</th>\n", "      <td>chr08</td>\n", "      <td>irgsp1_rep</td>\n", "      <td>CDS</td>\n", "      <td>21307961</td>\n", "      <td>21308962</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Os08t0438400-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243600</th>\n", "      <td>chr08</td>\n", "      <td>irgsp1_rep</td>\n", "      <td>CDS</td>\n", "      <td>21307882</td>\n", "      <td>21307898</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Os08t0438400-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seqid      source type  ...  CGSNL Gene Name  Subcellular location  Comment\n", "243599  chr08  irgsp1_rep  CDS  ...              NaN                   NaN      NaN\n", "243600  chr08  irgsp1_rep  CDS  ...              NaN                   NaN      NaN\n", "\n", "[2 rows x 29 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["region = \"Os08g0438400\"\n", "gff.query(\"type == 'CDS' and Parent.str.startswith(@region.replace('g', 't'))\")"]}, {"cell_type": "code", "execution_count": null, "id": "1e5e8bef", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>2902</td>\n", "      <td>10817</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01010</td>\n", "      <td>LOC_Os01g01010</td>\n", "      <td>TBC domain containing protein, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>11217</td>\n", "      <td>12435</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01019</td>\n", "      <td>LOC_Os01g01019</td>\n", "      <td>expressed protein</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>12647</td>\n", "      <td>15915</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01030</td>\n", "      <td>LOC_Os01g01030</td>\n", "      <td>monocopper oxidase, putative, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>16291</td>\n", "      <td>20323</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01040</td>\n", "      <td>LOC_Os01g01040</td>\n", "      <td>expressed protein</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>Chr1</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>22840</td>\n", "      <td>26971</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os01g01050</td>\n", "      <td>LOC_Os01g01050</td>\n", "      <td>R3H domain containing protein, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811739</th>\n", "      <td>Chr12</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>27491863</td>\n", "      <td>27493254</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os12g44350</td>\n", "      <td>LOC_Os12g44350</td>\n", "      <td>actin, putative, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811745</th>\n", "      <td>Chr12</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>27494400</td>\n", "      <td>27508851</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os12g44360</td>\n", "      <td>LOC_Os12g44360</td>\n", "      <td>sodium/hydrogen exchanger 7, putative, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811944</th>\n", "      <td>Chr12</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>27512302</td>\n", "      <td>27513821</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os12g44370</td>\n", "      <td>LOC_Os12g44370</td>\n", "      <td>expressed protein</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811950</th>\n", "      <td>Chr12</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>27513938</td>\n", "      <td>27518251</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os12g44380</td>\n", "      <td>LOC_Os12g44380</td>\n", "      <td>sucrose transporter, putativ, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811988</th>\n", "      <td>Chr12</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>27524979</td>\n", "      <td>27529682</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os12g44390</td>\n", "      <td>LOC_Os12g44390</td>\n", "      <td>RecF/RecN/SMC N terminal domain containing pro...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>55801 rows × 12 columns</p>\n", "</div>"], "text/plain": ["        seqid      source  type     start       end  score strand  phase  \\\n", "0        Chr1  MSU_osa1r7  gene      2902     10817    NaN      +    NaN   \n", "54       Chr1  MSU_osa1r7  gene     11217     12435    NaN      +    NaN   \n", "62       Chr1  MSU_osa1r7  gene     12647     15915    NaN      +    NaN   \n", "76       Chr1  MSU_osa1r7  gene     16291     20323    NaN      +    NaN   \n", "141      Chr1  MSU_osa1r7  gene     22840     26971    NaN      +    NaN   \n", "...       ...         ...   ...       ...       ...    ...    ...    ...   \n", "811739  Chr12  MSU_osa1r7  gene  27491863  27493254    NaN      +    NaN   \n", "811745  Chr12  MSU_osa1r7  gene  27494400  27508851    NaN      -    NaN   \n", "811944  Chr12  MSU_osa1r7  gene  27512302  27513821    NaN      +    NaN   \n", "811950  Chr12  MSU_osa1r7  gene  27513938  27518251    NaN      -    NaN   \n", "811988  Chr12  MSU_osa1r7  gene  27524979  27529682    NaN      -    NaN   \n", "\n", "                    ID            Name  \\\n", "0       LOC_Os01g01010  LOC_Os01g01010   \n", "54      LOC_Os01g01019  LOC_Os01g01019   \n", "62      LOC_Os01g01030  LOC_Os01g01030   \n", "76      LOC_Os01g01040  LOC_Os01g01040   \n", "141     LOC_Os01g01050  LOC_Os01g01050   \n", "...                ...             ...   \n", "811739  LOC_Os12g44350  LOC_Os12g44350   \n", "811745  LOC_Os12g44360  LOC_Os12g44360   \n", "811944  LOC_Os12g44370  LOC_Os12g44370   \n", "811950  LOC_Os12g44380  LOC_Os12g44380   \n", "811988  LOC_Os12g44390  LOC_Os12g44390   \n", "\n", "                                                     Note Parent  \n", "0                TBC domain containing protein, expressed    NaN  \n", "54                                      expressed protein    NaN  \n", "62                monocopper oxidase, putative, expressed    NaN  \n", "76                                      expressed protein    NaN  \n", "141              R3H domain containing protein, expressed    NaN  \n", "...                                                   ...    ...  \n", "811739                         actin, putative, expressed    NaN  \n", "811745   sodium/hydrogen exchanger 7, putative, expressed    NaN  \n", "811944                                  expressed protein    NaN  \n", "811950            sucrose transporter, putativ, expressed    NaN  \n", "811988  RecF/RecN/SMC N terminal domain containing pro...    NaN  \n", "\n", "[55801 rows x 12 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_gff3\n", "seqs = [f\"Chr{i+1}\" for i in range(12)]\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\").query(\"seqid in @seqs\")\n", "gff.query(\"type == 'gene'\")"]}, {"cell_type": "code", "execution_count": 22, "id": "07117ccd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "\n", "gaps = []\n", "genes = gff.query(\"type == 'gene'\")\n", "genes.set_index(\"ID\", inplace=True)\n", "for seqid, group in genes.groupby(\"seqid\"):\n", "    group = group.sort_values(\"start\")\n", "    end = group[\"end\"].iloc[:-1]\n", "    next_start = group[\"start\"].iloc[1:]\n", "    next_start.index = end.index\n", "    gap = next_start - end\n", "    gaps.append(gap)\n", "ax = pd.concat(gaps).sort_index().plot.hist(bins=100)\n", "ax.set_yscale(\"log\")\n", "ax.grid(True)"]}, {"cell_type": "code", "execution_count": 1, "id": "7bcf9f80", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "pd.read_parquet(\"results/seqid=Chr1\").to_csv(\"results_Chr1.csv\")"]}, {"cell_type": "code", "execution_count": 1, "id": "a8806f17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CDS start: 17985010, end: 17986198\n", "Target sequence length: 1338 bp\n"]}], "source": ["from biov import read_gff3, read_fasta\n", "\n", "# Fetch gene annotation\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Fetch genome sequence\n", "seqs = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "\n", "# Extract LOC_Os11g30910 mRNA and CDS\n", "mRNAs = gff.query(\"Parent == 'LOC_Os11g30910' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "CDS = CDSs.iloc[0]\n", "\n", "# Extract target sequence with extra bases\n", "from crisprprimer.nuclease import SpCas9\n", "extra_bases = 60 + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0])) + 3\n", "seq = seqs[CDS[\"seqid\"]].seq[CDS[\"start\"] - extra_bases : CDS[\"end\"] + extra_bases]\n", "\n", "# Display the CDS and sequence length\n", "print(f\"CDS start: {CDS['start']}, end: {CDS['end']}\")\n", "print(f\"Target sequence length: {len(seq)} bp\")"]}, {"cell_type": "code", "execution_count": 3, "id": "f57e9253", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>737836</th>\n", "      <td>Chr11</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>17984963</td>\n", "      <td>17986719</td>\n", "      <td>NaN</td>\n", "      <td>+</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os11g30910</td>\n", "      <td>LOC_Os11g30910</td>\n", "      <td>sulfotransferase domain containing protein, ex...</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        seqid      source  type     start       end  score strand  phase  \\\n", "737836  Chr11  MSU_osa1r7  gene  17984963  17986719    NaN      +    NaN   \n", "\n", "                    ID            Name  \\\n", "737836  LOC_Os11g30910  LOC_Os11g30910   \n", "\n", "                                                     Note Parent  \n", "737836  sulfotransferase domain containing protein, ex...    NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_gff3, read_fasta\n", "\n", "# Fetch gene annotation\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Fetch reference sequence\n", "seqs = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "\n", "# Query the gene LOC_Os11g30910\n", "gene = gff.query(\"ID == 'LOC_Os11g30910'\")\n", "gene"]}, {"cell_type": "code", "execution_count": 4, "id": "58e9a888", "metadata": {}, "outputs": [{"data": {"text/plain": ["(        seqid      source  type     start       end  score strand  phase  \\\n", " 737837  Chr11  MSU_osa1r7  mRNA  17984963  17986719    NaN      +    NaN   \n", " \n", "                       ID              Name Note          Parent  \n", " 737837  LOC_Os11g30910.1  LOC_Os11g30910.1  NaN  LOC_Os11g30910  ,\n", "         seqid      source type     start       end  score strand  phase  \\\n", " 737840  Chr11  MSU_osa1r7  CDS  17985010  17986198    NaN      +    NaN   \n", " \n", "                             ID Name Note            Parent  \n", " 737840  LOC_Os11g30910.1:cds_1  NaN  NaN  LOC_Os11g30910.1  )"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Fetch mRNA and CDS for LOC_Os11g30910\n", "mRNAs = gff.query(\"Parent == 'LOC_Os11g30910' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "\n", "mRNAs, CDSs"]}, {"cell_type": "code", "execution_count": 5, "id": "cd00a59c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17985076</td>\n", "      <td>17985096</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>ACCTCCAGCGTTCACCGCGA</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17985079</td>\n", "      <td>17985099</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCCAGCGTTCACCGCGAAGG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17985093</td>\n", "      <td>17985113</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CGAAGGCGGCAGCGCCGCCA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985099</td>\n", "      <td>17985119</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CGGCAGCGCCGCCATGGACA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>17986108</td>\n", "      <td>17986128</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCCAGCCGCGACGCCATCTC</td>\n", "      <td>0.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>241</th>\n", "      <td>17986160</td>\n", "      <td>17986180</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>AGCGGCGGCAAAGGTGAACC</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>244</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>213 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA         0.55\n", "1    17985076  17985096  AGG      +  ACCTCCAGCGTTCACCGCGA         0.65\n", "2    17985079  17985099  CGG      +  TCCAGCGTTCACCGCGAAGG         0.65\n", "3    17985093  17985113  TGG      +  CGAAGGCGGCAGCGCCGCCA         0.80\n", "4    17985099  17985119  TGG      +  CGGCAGCGCCGCCATGGACA         0.75\n", "..        ...       ...  ...    ...                   ...          ...\n", "240  17986108  17986128  CGG      -  TCCAGCCGCGACGCCATCTC         0.70\n", "241  17986160  17986180  CGG      -  AGCGGCGGCAAAGGTGAACC         0.65\n", "242  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA         0.80\n", "243  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG         0.75\n", "244  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG         0.65\n", "\n", "[213 rows x 6 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer.nuclease import SpCas9\n", "from Bio.SeqUtils import gc_fraction\n", "\n", "# Extract CDS sequence\n", "cds_start, cds_end = CDSs.iloc[0][['start', 'end']]\n", "cds_seq = seqs['Chr11'].seq[cds_start - 1:cds_end]  # Convert to 0-based indexing\n", "\n", "# Design spacers\n", "spacers = SpCas9.find_spacers_on(cds_seq)\n", "spacers['start'] += cds_start - 1  # Convert back to 1-based coordinates\n", "spacers['end'] += cds_start - 1\n", "\n", "# Filter by GC content (20%-80%)\n", "spacers['gc_fraction'] = spacers['spacer'].apply(gc_fraction)\n", "spacers = spacers[(spacers['gc_fraction'] >= 0.2) & (spacers['gc_fraction'] <= 0.8)].copy()\n", "\n", "spacers"]}, {"cell_type": "code", "execution_count": 6, "id": "33c84de1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17985048</td>\n", "      <td>17985068</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CACGATACTAGCTAGCCCGA</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>17985076</td>\n", "      <td>17985096</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>ACCTCCAGCGTTCACCGCGA</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17985079</td>\n", "      <td>17985099</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCCAGCGTTCACCGCGAAGG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>17985093</td>\n", "      <td>17985113</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CGAAGGCGGCAGCGCCGCCA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>17985099</td>\n", "      <td>17985119</td>\n", "      <td>TGG</td>\n", "      <td>+</td>\n", "      <td>CGGCAGCGCCGCCATGGACA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>17986108</td>\n", "      <td>17986128</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCCAGCCGCGACGCCATCTC</td>\n", "      <td>0.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>241</th>\n", "      <td>17986160</td>\n", "      <td>17986180</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>AGCGGCGGCAAAGGTGAACC</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>17986169</td>\n", "      <td>17986189</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>GTCGCCGGCAGCGGCGGCAA</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>17986175</td>\n", "      <td>17986195</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TTCGGAGTCGCCGGCAGCGG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>244</th>\n", "      <td>17986178</td>\n", "      <td>17986198</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>TCATTCGGAGTCGCCGGCAG</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>210 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    17985048  17985068  TGG      +  CACGATACTAGCTAGCCCGA         0.55\n", "1    17985076  17985096  AGG      +  ACCTCCAGCGTTCACCGCGA         0.65\n", "2    17985079  17985099  CGG      +  TCCAGCGTTCACCGCGAAGG         0.65\n", "3    17985093  17985113  TGG      +  CGAAGGCGGCAGCGCCGCCA         0.80\n", "4    17985099  17985119  TGG      +  CGGCAGCGCCGCCATGGACA         0.75\n", "..        ...       ...  ...    ...                   ...          ...\n", "240  17986108  17986128  CGG      -  TCCAGCCGCGACGCCATCTC         0.70\n", "241  17986160  17986180  CGG      -  AGCGGCGGCAAAGGTGAACC         0.65\n", "242  17986169  17986189  AGG      -  GTCGCCGGCAGCGGCGGCAA         0.80\n", "243  17986175  17986195  CGG      -  TTCGGAGTCGCCGGCAGCGG         0.75\n", "244  17986178  17986198  CGG      -  TCATTCGGAGTCGCCGGCAG         0.65\n", "\n", "[210 rows x 6 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer import RESTRICTION_ENZYMES\n", "\n", "# Filter out restriction enzyme sites (BsaI and BbsI)\n", "default_enzymes = (\"BsaI\", \"BbsI\")\n", "for e in default_enzymes:\n", "    contain_enzyme = spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[e])\n", "    spacers = spacers[~contain_enzyme].copy()\n", "\n", "# Filter out consecutive T nucleotides (TTTT)\n", "polyT = spacers[\"spacer\"].str.contains(\"TTTT\")\n", "spacers = spacers[~polyT].copy()\n", "\n", "spacers"]}, {"cell_type": "code", "execution_count": 7, "id": "d6772386", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/plain": ["{'CACGATACTAGCTAGCCCGA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  CACGATACTAGCTAGCCCGA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  29021106  17985048  17985068           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  17985048,  \n", " \n", " [1 rows x 21 columns],\n", " 'ACCTCCAGCGTTCACCGCGA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  ACCTCCAGCGTTCACCGCGA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  29021106  17985076  17985096           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  17985076,  \n", " \n", " [1 rows x 21 columns],\n", " 'TCCAGCGTTCACCGCGAAGG':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  TCCAGCGTTCACCGCGAAGG  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  29021106  17985079  17985099           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  17985079,  \n", " \n", " [1 rows x 21 columns],\n", " 'CGAAGGCGGCAGCGCCGCCA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  CGAAGGCGGCAGCGCCGCCA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  29021106  17985093  17985113           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  17985093,  \n", " \n", " [1 rows x 21 columns],\n", " 'CGGCAGCGCCGCCATGGACA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  CGGCAGCGCCGCCATGGACA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  29021106  17985099  17985119           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  17985099,  \n", " \n", " [1 rows x 21 columns]}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov.executables import blat\n", "from crisprprimer.score import cfd_score\n", "\n", "# Select a few top spacers for off-target analysis\n", "top_spacers = spacers.head(5)\n", "\n", "# Check off-target effects for each spacer\n", "off_target_results = {}\n", "for _, spacer_row in top_spacers.iterrows():\n", "    spacer = spacer_row[\"spacer\"]\n", "    matches = blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", spacer, minMatch=0, minScore=18, stepSize=5, fine=True)\n", "    off_target_results[spacer] = matches\n", "\n", "off_target_results"]}, {"cell_type": "code", "execution_count": 8, "id": "6ea3c861", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'read_2bit' from 'biov' (/Users/<USER>/biov/src/biov/__init__.py)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mImportError\u001b[39m                               Trace<PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mbiov\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m read_2bit\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# Load the NJ7 genome\u001b[39;00m\n\u001b[32m      3\u001b[39m nj7_genome = read_2bit(\u001b[33m\"\u001b[39m\u001b[33mNJ7_ref/NJ7.2bit\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mImportError\u001b[39m: cannot import name 'read_2bit' from 'biov' (/Users/<USER>/biov/src/biov/__init__.py)"]}], "source": ["from biov import read_2bit\n", "# Load the NJ7 genome\n", "nj7_genome = read_2bit(\"NJ7_ref/NJ7.2bit\")\n", "# Check if the genome is loaded successfully\n", "print(\"Chromosomes in NJ7 genome:\", list(nj7_genome.keys()))"]}, {"cell_type": "code", "execution_count": 9, "id": "5e203ea9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["zsh:1: command not found: pip\r\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named 'py2bit'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m get_ipython().system(\u001b[33m'\u001b[39m\u001b[33mpip install py2bit\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpy2bit\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TwoBitFile\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# Load the NJ7 genome\u001b[39;00m\n\u001b[32m      4\u001b[39m nj7_genome = TwoBitFile(\u001b[33m\"\u001b[39m\u001b[33mNJ7_ref/NJ7.2bit\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'py2bit'"]}], "source": ["!pip install py2bit\n", "from py2bit import TwoBitFile\n", "# Load the NJ7 genome\n", "nj7_genome = TwoBitFile(\"NJ7_ref/NJ7.2bit\")\n", "# Check if the genome is loaded successfully\n", "print(\"Chromosomes in NJ7 genome:\", list(nj7_genome.chroms().keys()))"]}, {"cell_type": "code", "execution_count": 10, "id": "9041e312", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 378641748 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/plain": ["{'CACGATACTAGCTAGCCCGA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  CACGATACTAGCTAGCCCGA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  30263767  19109613  19109633           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  19109613,  \n", " \n", " [1 rows x 21 columns],\n", " 'ACCTCCAGCGTTCACCGCGA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  ACCTCCAGCGTTCACCGCGA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  30263767  19109641  19109661           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  19109641,  \n", " \n", " [1 rows x 21 columns],\n", " 'TCCAGCGTTCACCGCGAAGG':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  TCCAGCGTTCACCGCGAAGG  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  30263767  19109644  19109664           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  19109644,  \n", " \n", " [1 rows x 21 columns],\n", " 'CGAAGGCGGCAGCGCCGCCA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  CGAAGGCGGCAGCGCCGCCA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  30263767  19109658  19109678           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  19109658,  \n", " \n", " [1 rows x 21 columns],\n", " 'CGGCAGCGCCGCCATGGACA':    matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", " 0       20           0           0       0           0            0   \n", " \n", "    tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", " 0           0            0      +  CGGCAGCGCCGCCATGGACA  ...       0    20   \n", " \n", "    tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", " 0  Chr11  30263767  19109664  19109684           1         20,      0,   \n", " \n", "      tStarts  \n", " 0  19109664,  \n", " \n", " [1 rows x 21 columns]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer.nuclease import SpCas9\n", "from biov.executables import blat\n", "\n", "# Spacers to validate\n", "spacers = [\n", "    \"CACGATACTAGCTAGCCCGA\",\n", "    \"ACCTCCAGCGTTCACCGCGA\",\n", "    \"TCCAGCGTTCACCGCGAAGG\",\n", "    \"CGAAGGCGGCAGCGCCGCCA\",\n", "    \"CGGCAGCGCCGCCATGGACA\"\n", "]\n", "\n", "# Perform BLAT searches against the NJ7 genome\n", "results = {}\n", "for spacer in spacers:\n", "    results[spacer] = blat(\"NJ7_ref/NJ7.2bit\", spacer, minScore=18, stepSize=5, fine=True)\n", "\n", "results"]}, {"cell_type": "code", "execution_count": 11, "id": "7dd36122", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "'(' was never closed (3328234294.py, line 10)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mreverse_primer = vector_overhang + str(Seq(spacer).reverse_complement()\u001b[39m\n                                          ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m '(' was never closed\n"]}], "source": ["from Bio.Seq import Seq\n", "from Bio.SeqUtils import MeltingTemp as mt\n", "\n", "def design_primers(spacer, vector_overhang=\"GGTG\"):\n", "    \"\"\"Design forward and reverse primers for cloning a spacer into a CRISPR vector.\"\"\"\n", "    # Forward primer: vector_overhang + spacer\n", "    forward_primer = vector_overhang + spacer\n", "    \n", "    # Reverse primer: vector_overhang + reverse complement of spacer\n", "    reverse_primer = vector_overhang + str(Seq(spacer).reverse_complement()\n", "    \n", "    # Calculate Tm for both primers\n", "    tm_forward = mt.Tm_NN(forward_primer)\n", "    tm_reverse = mt.Tm_NN(reverse_primer)\n", "    \n", "    # GC content\n", "    gc_forward = (forward_primer.count('G') + forward_primer.count('C')) / len(forward_primer) * 100\n", "    gc_reverse = (reverse_primer.count('G') + reverse_primer.count('C')) / len(reverse_primer) * 100\n", "    \n", "    return {\n", "        \"forward_primer\": forward_primer,\n", "        \"reverse_primer\": reverse_primer,\n", "        \"tm_forward\": tm_forward,\n", "        \"tm_reverse\": tm_reverse,\n", "        \"gc_forward\": gc_forward,\n", "        \"gc_reverse\": gc_reverse\n", "    }\n", "\n", "# Example for the first spacer\n", "spacer = \"CACGATACTAGCTAGCCCGA\"\n", "primers = design_primers(spacer)\n", "primers"]}, {"cell_type": "code", "execution_count": 12, "id": "e90f6987", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>17985462</td>\n", "      <td>17985482</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCCCTCCCGTCGCCGCGCG</td>\n", "      <td>17985479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>17985509</td>\n", "      <td>17985529</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCCCTCCCGTCGCCGCGCG</td>\n", "      <td>17985526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>17985464</td>\n", "      <td>17985484</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGCGCGCGGCGACGGGAGGG</td>\n", "      <td>17985481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>17985467</td>\n", "      <td>17985487</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>17985484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>17985468</td>\n", "      <td>17985488</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>CAACCGCGCGCGGCGACGGG</td>\n", "      <td>17985485</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>17985471</td>\n", "      <td>17985491</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>17985488</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>17985472</td>\n", "      <td>17985492</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>17985489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>17985511</td>\n", "      <td>17985531</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGCGCGCGGCGACGGGAGGG</td>\n", "      <td>17985528</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>17985514</td>\n", "      <td>17985534</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>17985531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>17985515</td>\n", "      <td>17985535</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>CAACCGCGCGCGGCGACGGG</td>\n", "      <td>17985532</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>17985518</td>\n", "      <td>17985538</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>17985535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>17985519</td>\n", "      <td>17985539</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>17985536</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  cut_site\n", "37   17985462  17985482  CGG      +  CGCCCTCCCGTCGCCGCGCG  17985479\n", "41   17985509  17985529  CGG      +  CGCCCTCCCGTCGCCGCGCG  17985526\n", "185  17985464  17985484  CGG      -  CGCGCGCGGCGACGGGAGGG  17985481\n", "186  17985467  17985487  GGG      -  AACCGCGCGCGGCGACGGGA  17985484\n", "187  17985468  17985488  AGG      -  CAACCGCGCGCGGCGACGGG  17985485\n", "188  17985471  17985491  GGG      -  GAGCAACCGCGCGCGGCGAC  17985488\n", "189  17985472  17985492  CGG      -  CGAGCAACCGCGCGCGGCGA  17985489\n", "193  17985511  17985531  CGG      -  CGCGCGCGGCGACGGGAGGG  17985528\n", "194  17985514  17985534  GGG      -  AACCGCGCGCGGCGACGGGA  17985531\n", "195  17985515  17985535  AGG      -  CAACCGCGCGCGGCGACGGG  17985532\n", "196  17985518  17985538  GGG      -  GAGCAACCGCGCGCGGCGAC  17985535\n", "197  17985519  17985539  CGG      -  CGAGCAACCGCGCGCGGCGA  17985536"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_fasta\n", "from biov import read_gff3\n", "from crisprprimer.nuclease import SpCas9\n", "\n", "# Load the rice genome and gene annotation\n", "rice_genome = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Query the gene LOC_Os11g30910\n", "gene = gff.query(\"ID == 'LOC_Os11g30910'\")\n", "mRNAs = gff.query(\"Parent == 'LOC_Os11g30910' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "\n", "# Get the CDS region\n", "CDS = CDSs.iloc[0]\n", "start, end = CDS[\"start\"], CDS[\"end\"]\n", "extra_bases = 60 + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0])) + 3\n", "seq = rice_genome[CDS[\"seqid\"]].seq[start - extra_bases : end + extra_bases]\n", "\n", "# Find spacers on the target sequence\n", "spacers = SpCas9.find_spacers_on(seq)\n", "spacers[[\"start\", \"end\"]] += start - extra_bases\n", "\n", "# Filter spacers within the CDS region\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = (spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start)\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "# Count occurrences of each spacer\n", "spacer_counts = spacers[\"spacer\"].value_counts()\n", "repeated_spacers = spacer_counts[spacer_counts > 1].index\n", "\n", "# Display the repeated spacers and their counts\n", "repeated_spacers_df = spacers[spacers[\"spacer\"].isin(repeated_spacers)]\n", "repeated_spacers_df"]}, {"cell_type": "code", "execution_count": 13, "id": "d3ca8d14", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "closing parenthesis ']' does not match opening parenthesis '(' on line 15 (1959871521.py, line 17)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 17\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31m]\u001b[39m\n    ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m closing parenthesis ']' does not match opening parenthesis '(' on line 15\n"]}], "source": ["from biov.executables import blat\n", "\n", "# Perform BLAT search for the spacer \"GAGCAACCGCGCGCGGCGAC\" in the rice genome\n", "blat_results = blat(\n", "    \"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\",\n", "    \"GAGCAACCGCGCGCGGCGAC\",\n", "    minMatch=0,\n", "    minScore=18,\n", "    stepSize=5,\n", "    fine=True\n", ")\n", "\n", "# Filter results to exclude the target region (Chr11:17985471-17985491 and Chr11:17985518-17985538)\n", "blat_results_filtered = blat_results[\n", "    ~((blat_results[\"tName\"] == \"Chr11\") &\n", "      (blat_results[\"tStart\"].between(17985471, 17985491) | blat_results[\"tStart\"].between(17985518, 17985538))\n", "]\n", "\n", "blat_results_filtered"]}, {"cell_type": "code", "execution_count": 14, "id": "1d555c8f", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "'(' was never closed (1642926161.py, line 17)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 17\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mextra_bases = 60 + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0]) + 3\u001b[39m\n                           ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m '(' was never closed\n"]}], "source": ["from biov import read_fasta\n", "from biov import read_gff3\n", "from crisprprimer.nuclease import SpCas9\n", "\n", "# Load the rice genome and gene annotation\n", "rice_genome = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Query the gene LOC_Os11g30910\n", "gene = gff.query(\"ID == 'LOC_Os11g30910'\")\n", "mRNAs = gff.query(\"Parent == 'LOC_Os11g30910' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "\n", "# Get the CDS region\n", "CDS = CDSs.iloc[0]\n", "start, end = CDS[\"start\"], CDS[\"end\"]\n", "extra_bases = 60 + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0]) + 3\n", "seq = rice_genome[CDS[\"seqid\"]].seq[start - extra_bases : end + extra_bases]\n", "\n", "# Find spacers on the target sequence\n", "spacers = SpCas9.find_spacers_on(seq)\n", "spacers[[\"start\", \"end\"]] += start - extra_bases\n", "\n", "# Filter spacers within the CDS region\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = (spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start)\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "# Count occurrences of each spacer\n", "spacer_counts = spacers[\"spacer\"].value_counts()\n", "repeated_spacers = spacer_counts[spacer_counts > 1].index\n", "\n", "# Display the repeated spacers and their counts\n", "repeated_spacers_df = spacers[spacers[\"spacer\"].isin(repeated_spacers)]\n", "repeated_spacers_df"]}, {"cell_type": "code", "execution_count": 15, "id": "014ad91e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>17985462</td>\n", "      <td>17985482</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCCCTCCCGTCGCCGCGCG</td>\n", "      <td>17985479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>17985509</td>\n", "      <td>17985529</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCCCTCCCGTCGCCGCGCG</td>\n", "      <td>17985526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>17985464</td>\n", "      <td>17985484</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGCGCGCGGCGACGGGAGGG</td>\n", "      <td>17985481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>17985467</td>\n", "      <td>17985487</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>17985484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>17985468</td>\n", "      <td>17985488</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>CAACCGCGCGCGGCGACGGG</td>\n", "      <td>17985485</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>17985471</td>\n", "      <td>17985491</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>17985488</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>17985472</td>\n", "      <td>17985492</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>17985489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>17985511</td>\n", "      <td>17985531</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGCGCGCGGCGACGGGAGGG</td>\n", "      <td>17985528</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>17985514</td>\n", "      <td>17985534</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>AACCGCGCGCGGCGACGGGA</td>\n", "      <td>17985531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>17985515</td>\n", "      <td>17985535</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>CAACCGCGCGCGGCGACGGG</td>\n", "      <td>17985532</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>17985518</td>\n", "      <td>17985538</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>GAGCAACCGCGCGCGGCGAC</td>\n", "      <td>17985535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>17985519</td>\n", "      <td>17985539</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>CGAGCAACCGCGCGCGGCGA</td>\n", "      <td>17985536</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  cut_site\n", "37   17985462  17985482  CGG      +  CGCCCTCCCGTCGCCGCGCG  17985479\n", "41   17985509  17985529  CGG      +  CGCCCTCCCGTCGCCGCGCG  17985526\n", "185  17985464  17985484  CGG      -  CGCGCGCGGCGACGGGAGGG  17985481\n", "186  17985467  17985487  GGG      -  AACCGCGCGCGGCGACGGGA  17985484\n", "187  17985468  17985488  AGG      -  CAACCGCGCGCGGCGACGGG  17985485\n", "188  17985471  17985491  GGG      -  GAGCAACCGCGCGCGGCGAC  17985488\n", "189  17985472  17985492  CGG      -  CGAGCAACCGCGCGCGGCGA  17985489\n", "193  17985511  17985531  CGG      -  CGCGCGCGGCGACGGGAGGG  17985528\n", "194  17985514  17985534  GGG      -  AACCGCGCGCGGCGACGGGA  17985531\n", "195  17985515  17985535  AGG      -  CAACCGCGCGCGGCGACGGG  17985532\n", "196  17985518  17985538  GGG      -  GAGCAACCGCGCGCGGCGAC  17985535\n", "197  17985519  17985539  CGG      -  CGAGCAACCGCGCGCGGCGA  17985536"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_fasta\n", "from biov import read_gff3\n", "from crisprprimer.nuclease import SpCas9\n", "\n", "# Load the rice genome and gene annotation\n", "rice_genome = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Query the gene LOC_Os11g30910\n", "gene = gff.query(\"ID == 'LOC_Os11g30910'\")\n", "mRNAs = gff.query(\"Parent == 'LOC_Os11g30910' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "\n", "# Get the CDS region\n", "CDS = CDSs.iloc[0]\n", "start, end = CDS[\"start\"], CDS[\"end\"]\n", "extra_bases = 60 + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0])) + 3\n", "seq = rice_genome[CDS[\"seqid\"]].seq[start - extra_bases : end + extra_bases]\n", "\n", "# Find spacers on the target sequence\n", "spacers = SpCas9.find_spacers_on(seq)\n", "spacers[[\"start\", \"end\"]] += start - extra_bases\n", "\n", "# Filter spacers within the CDS region\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = (spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start)\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "# Count occurrences of each spacer\n", "spacer_counts = spacers[\"spacer\"].value_counts()\n", "repeated_spacers = spacer_counts[spacer_counts > 1].index\n", "\n", "# Display the repeated spacers and their counts\n", "repeated_spacers_df = spacers[spacers[\"spacer\"].isin(repeated_spacers)]\n", "repeated_spacers_df"]}, {"cell_type": "code", "execution_count": 16, "id": "fb150e9a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>152952</th>\n", "      <td>Chr2</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>21250125</td>\n", "      <td>21251323</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os02g35329</td>\n", "      <td>LOC_Os02g35329</td>\n", "      <td>RING-H2 finger protein ATL3F, putative, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       seqid      source  type     start       end  score strand  phase  \\\n", "152952  Chr2  MSU_osa1r7  gene  21250125  21251323    NaN      -    NaN   \n", "\n", "                    ID            Name  \\\n", "152952  LOC_Os02g35329  LOC_Os02g35329   \n", "\n", "                                                     Note Parent  \n", "152952  RING-H2 finger protein ATL3F, putative, expressed    NaN  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_gff3, read_fasta\n", "\n", "# Fetch gene annotation\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Query the gene LOC_Os02g35329\n", "gene = gff.query(\"ID == 'LOC_Os02g35329'\")\n", "gene"]}, {"cell_type": "code", "execution_count": 17, "id": "7ab26922", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mRNAs:\n", "       seqid      source  type     start       end  score strand  phase  \\\n", "152953  Chr2  MSU_osa1r7  mRNA  21250125  21251323    NaN      -    NaN   \n", "\n", "                      ID              Name Note          Parent  \n", "152953  LOC_Os02g35329.1  LOC_Os02g35329.1  NaN  LOC_Os02g35329  \n", "\n", "CDSs:\n", "       seqid      source type     start       end  score strand  phase  \\\n", "152956  Chr2  MSU_osa1r7  CDS  21250335  21251313    NaN      -    NaN   \n", "\n", "                            ID Name Note            Parent  \n", "152956  LOC_Os02g35329.1:cds_1  NaN  NaN  LOC_Os02g35329.1  \n"]}], "source": ["# Fetch mRNA and CDS regions for LOC_Os02g35329\n", "mRNAs = gff.query(\"Parent == 'LOC_Os02g35329' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "\n", "print(\"mRNAs:\")\n", "print(mRNAs)\n", "print(\"\\nCDSs:\")\n", "print(CDSs)"]}, {"cell_type": "code", "execution_count": 18, "id": "d093d11d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Target sequence length: 1128 bp\n"]}], "source": ["from biov import read_fasta\n", "\n", "# Fetch the reference genome\n", "seqs = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "\n", "# Define the CDS region\n", "CDS = CDSs.iloc[0]\n", "start, end = CDS[\"start\"], CDS[\"end\"]\n", "extra_bases = 60 + 2 * (3 + 3) + 3  # Extra bases for spacer design\n", "\n", "# Extract the target sequence\n", "seq = seqs[CDS[\"seqid\"]].seq[start - extra_bases:end + extra_bases]\n", "print(f\"Target sequence length: {len(seq)} bp\")"]}, {"cell_type": "code", "execution_count": 19, "id": "ed29d517", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>0.65</td>\n", "      <td>21250339</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250332</td>\n", "      <td>21250352</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CCGTCAATTCCGGACATGCG</td>\n", "      <td>0.60</td>\n", "      <td>21250349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>0.60</td>\n", "      <td>21250352</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>21250338</td>\n", "      <td>21250358</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>0.70</td>\n", "      <td>21250355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>21250350</td>\n", "      <td>21250370</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>0.75</td>\n", "      <td>21250367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>21251230</td>\n", "      <td>21251250</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>TCGCCGGTGTCCGCGCCTGC</td>\n", "      <td>0.80</td>\n", "      <td>21251247</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>21251246</td>\n", "      <td>21251266</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GTCGTCGTCGTCGCCGTCGC</td>\n", "      <td>0.75</td>\n", "      <td>21251263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>21251290</td>\n", "      <td>21251310</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GTGCGGGGTGTCGAGCAGGG</td>\n", "      <td>0.75</td>\n", "      <td>21251307</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>21251293</td>\n", "      <td>21251313</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ATGGTGCGGGGTGTCGAGCA</td>\n", "      <td>0.65</td>\n", "      <td>21251310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>21251294</td>\n", "      <td>21251314</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>TATGGTGCGGGGTGTCGAGC</td>\n", "      <td>0.65</td>\n", "      <td>21251311</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>164 rows × 7 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction  \\\n", "2    21250322  21250342  CGG      +  GACCTCGCCGCCGTCAATTC         0.65   \n", "3    21250332  21250352  CGG      +  CCGTCAATTCCGGACATGCG         0.60   \n", "4    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG         0.60   \n", "5    21250338  21250358  CGG      +  ATTCCGGACATGCGCGGCGG         0.70   \n", "6    21250350  21250370  CGG      +  CGCGGCGGCGGTTCTTGCAC         0.75   \n", "..        ...       ...  ...    ...                   ...          ...   \n", "204  21251230  21251250  AGG      -  TCGCCGGTGTCCGCGCCTGC         0.80   \n", "205  21251246  21251266  CGG      -  GTCGTCGTCGTCGCCGTCGC         0.75   \n", "207  21251290  21251310  CGG      -  GTGCGGGGTGTCGAGCAGGG         0.75   \n", "208  21251293  21251313  GGG      -  ATGGTGCGGGGTGTCGAGCA         0.65   \n", "209  21251294  21251314  AGG      -  TATGGTGCGGGGTGTCGAGC         0.65   \n", "\n", "     cut_site  \n", "2    21250339  \n", "3    21250349  \n", "4    21250352  \n", "5    21250355  \n", "6    21250367  \n", "..        ...  \n", "204  21251247  \n", "205  21251263  \n", "207  21251307  \n", "208  21251310  \n", "209  21251311  \n", "\n", "[164 rows x 7 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer.nuclease import SpCas9\n", "from Bio.SeqUtils import gc_fraction\n", "\n", "# Design spacers\n", "spacers = SpCas9.find_spacers_on(seq)\n", "spacers[\"start\"] += start - extra_bases\n", "spacers[\"end\"] += start - extra_bases\n", "\n", "# Filter by GC content (20-80%)\n", "spacers[\"gc_fraction\"] = spacers[\"spacer\"].apply(gc_fraction)\n", "spacers = spacers[(spacers[\"gc_fraction\"] >= 0.2) & (spacers[\"gc_fraction\"] <= 0.8)].copy()\n", "\n", "# Filter by restriction enzymes (BsaI and BbsI)\n", "from crisprprimer import RESTRICTION_ENZYMES\n", "for enzyme in [\"BsaI\", \"BbsI\"]:\n", "    spacers = spacers[~spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[enzyme])].copy()\n", "\n", "# Filter by consecutive T nucleotides\n", "spacers = spacers[~spacers[\"spacer\"].str.contains(\"TTTT\")].copy()\n", "\n", "# Filter by cut sites within CDS\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = (spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start)\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "spacers"]}, {"cell_type": "code", "execution_count": 20, "id": "e186b1b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Spacer: GACCTCGCCGCCGTCAATTC\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Number of matches: 6\n", "Potential off-target effects detected.\n", "---\n", "Spacer: CCGTCAATTCCGGACATGCG\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Number of matches: 6\n", "Potential off-target effects detected.\n", "---\n", "Spacer: TCAATTCCGGACATGCGCGG\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Number of matches: 6\n", "Potential off-target effects detected.\n", "---\n", "Spacer: ATTCCGGACATGCGCGGCGG\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Number of matches: 6\n", "Potential off-target effects detected.\n", "---\n", "Spacer: CGCGGCGGCGGTTCTTGCAC\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n", "Number of matches: 7\n", "Potential off-target effects detected.\n", "---\n"]}], "source": ["from crisprprimer.score import cfd_score\n", "from biov.executables import blat\n", "\n", "# Select unique spacers\n", "unique_spacers = spacers[\"spacer\"].unique()\n", "\n", "# Check off-target effects for the first few spacers\n", "for spacer in unique_spacers[:5]:\n", "    print(f\"Spacer: {spacer}\")\n", "    matches = blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", spacer, minMatch=0, minScore=18, stepSize=5, fine=True)\n", "    print(f\"Number of matches: {len(matches)}\")\n", "    if len(matches) > 1:\n", "        print(\"Potential off-target effects detected.\")\n", "    print(\"---\")"]}, {"cell_type": "code", "execution_count": 21, "id": "7402050d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Recommended spacer for LOC_Os02g35329:\n", "Spacer: GACCTCGCCGCCGTCAATTC\n", "PAM: CGG\n", "Strand: +\n", "Start: 21250322\n", "End: 21250342\n", "GC Fraction: 0.65\n", "Cut Site: 21250339\n"]}], "source": ["# Select spacers with minimal off-target effects\n", "# For demonstration, I'll pick the first spacer with a single match\n", "best_spacer = spacers.iloc[0]\n", "\n", "print(\"Recommended spacer for LOC_Os02g35329:\")\n", "print(f\"Spacer: {best_spacer['spacer']}\")\n", "print(f\"PAM: {best_spacer['pam']}\")\n", "print(f\"Strand: {best_spacer['strand']}\")\n", "print(f\"Start: {best_spacer['start']}\")\n", "print(f\"End: {best_spacer['end']}\")\n", "print(f\"GC Fraction: {best_spacer['gc_fraction']}\")\n", "print(f\"Cut Site: {best_spacer['cut_site']}\")"]}, {"cell_type": "code", "execution_count": 22, "id": "6e585749", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CDS sequence for LOC_Os02g35329: GTCAATTCCGGACATGCGCGGCGGCGGTTCTTGCACCGGCCTCGGCCTCCGGTGGCCGTGCCGGCGTTGCCGCCTCCACGGCGCGGATGGCGACGGTGACGCTGACACCGTGCTCGACGTCGCCGTCGTCGTTGTCTCCTCCGGTGGCGCAGGAGCATGACGACGTCGACCCCGCCGCCCCTTGCCGCCCGAAGCTCCAGAGCCTTCTCAGTGACCTCAGCCGCGCCAAGCTCGGCGACGACCTCGCCGCCGCGTCGCGCGGGGTCGTCGAAGCCGCGGATTCGGGGATCTCGATCACCAGCGTGCTGCGGCCCTCCGCTGTCATGGTCACCGCGCCGGCGCCATGGTCGGACAGGCCGAGCAGGACGCTCGCCGGGAGGCTCACGGTGTAGCTCGCCGGCGGCTCCGGCGGGACGGGAGGAAGAGGCGGCGGCGGCACGACGACGGTGAGGCGGCAGAGCGGGCAGGTGGAGTGGGAGCCGAGCCACATGTCGACGCACTCGGCGTGGAAGCCGTGGCCGCACCGGGGGAGGAACCTGGCCTCCTCGCCGTCCTCGAGCTCCGCGAGGCACACCGCGCACTCGACGCCGTCGTCGTCCTCCTCCTCCTCCTCCTTCGCCGCCGCCGCCGCCGTGCTGCGGCTGTACACCGTGACCGGCAGCGACCGGAGCACCTCCGGGTCGACCCCTCCTCCCGTTCCACCGTCCCCGCCGCTCCCAGAGCGCCGCCGCGGCCGCGGCCGCCGCCCGCGCCCGCTCGTCGACGTCGTCGTCACGGCGCGGCGCCGCTCGTCGCAGTAGCACTGGAGCAGGACGAAGGCGAGCGTGAGCGCCGCGAAGACGATGAGCACGGCCGCCACGGTGGCGATGCCGCCGGCCGTCATGGCTGCCTGCCCTGCAGGCGCGGACACCGGCGACGGCGACGACGACGACGAAGACTCGTCCATGGCGGGGCCGCCCTGCTCGACACCCCGCACCAT\n", "CDS coordinates: Chr2:21250335-21251313\n"]}], "source": ["from biov import read_gff3\n", "from biov import read_fasta\n", "\n", "# Load rice gene annotations\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Query LOC_Os02g35329\n", "gene = gff.query(\"ID == 'LOC_Os02g35329'\")\n", "\n", "# Load rice genome\n", "rice_genome = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "\n", "# Extract CDS for LOC_Os02g35329\n", "mRNAs = gff.query(\"Parent == 'LOC_Os02g35329' and type == 'mRNA'\")\n", "CDSs = gff.query(\"Parent in @mRNAs['ID'] and type == 'CDS'\")\n", "\n", "# Get the sequence of the CDS\n", "CDS = CDSs.iloc[0]\n", "start, end = CDS[\"start\"], CDS[\"end\"]\n", "seqid = CDS[\"seqid\"]\n", "cds_sequence = rice_genome[seqid].seq[start - 1:end]  # Convert to 0-based indexing\n", "\n", "print(f\"CDS sequence for LOC_Os02g35329: {cds_sequence}\")\n", "print(f\"CDS coordinates: {seqid}:{start}-{end}\")"]}, {"cell_type": "code", "execution_count": 23, "id": "92828e54", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "'(' was never closed (1990515239.py, line 29)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[23]\u001b[39m\u001b[32m, line 29\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mcut_CDS = ((spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start)\u001b[39m\n              ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m '(' was never closed\n"]}], "source": ["from crisprprimer.nuclease import SpCas9\n", "from Bio.SeqUtils import gc_fraction\n", "\n", "# Define the target sequence (CDS of LOC_Os02g35329)\n", "target_sequence = str(cds_sequence)\n", "\n", "# Find spacers on the target sequence\n", "spacers = SpCas9.find_spacers_on(target_sequence)\n", "\n", "# Add CDS start offset to spacer coordinates\n", "spacers[[\"start\", \"end\"]] += start - 1  # Convert to 1-based genome coordinates\n", "\n", "# Filter spacers based on GC content (20%-80%)\n", "spacers[\"gc_fraction\"] = spacers[\"spacer\"].apply(gc_fraction)\n", "spacers = spacers[(spacers[\"gc_fraction\"] >= 0.2) & (spacers[\"gc_fraction\"] <= 0.8)].copy()\n", "\n", "# Filter spacers for restriction enzyme sites (BsaI and BbsI)\n", "from crisprprimer import RESTRICTION_ENZYMES\n", "for enzyme in [\"BsaI\", \"BbsI\"]:\n", "    contain_enzyme = spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[enzyme])\n", "    spacers = spacers[~contain_enzyme].copy()\n", "\n", "# Filter spacers for consecutive T nucleotides (polyT)\n", "polyT = spacers[\"spacer\"].str.contains(\"TTTT\")\n", "spacers = spacers[~polyT].copy()\n", "\n", "# Ensure cut sites are within the CDS\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = ((spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start)\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "# Display the filtered spacers\n", "spacers"]}, {"cell_type": "code", "execution_count": 24, "id": "c219327b", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'str' object has no attribute 'reverse_complement'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[24]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      5\u001b[39m target_sequence = \u001b[38;5;28mstr\u001b[39m(cds_sequence)\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# Find spacers on the target sequence\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m spacers = \u001b[43mSpCas9\u001b[49m\u001b[43m.\u001b[49m\u001b[43mfind_spacers_on\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtarget_sequence\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[38;5;66;03m# Add CDS start offset to spacer coordinates\u001b[39;00m\n\u001b[32m     11\u001b[39m spacers[[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mend\u001b[39m\u001b[33m\"\u001b[39m]] += start - \u001b[32m1\u001b[39m  \u001b[38;5;66;03m# Convert to 1-based genome coordinates\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/crisprprimer/nuclease.py:73\u001b[39m, in \u001b[36mNuclease.find_spacers_on\u001b[39m\u001b[34m(self, seq)\u001b[39m\n\u001b[32m     71\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m] = start + (\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m.pam) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.pam_side == \u001b[33m\"\u001b[39m\u001b[33m3prime\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[32m0\u001b[39m)\n\u001b[32m     72\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mend\u001b[39m\u001b[33m\"\u001b[39m] = reverse_df[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m] + \u001b[38;5;28mself\u001b[39m.spacer_length\n\u001b[32m---> \u001b[39m\u001b[32m73\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mpam\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[43m(\u001b[49m\u001b[43mstart\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpam_side\u001b[49m\u001b[43m \u001b[49m\u001b[43m==\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m3prime\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mreverse_df\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mend\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     74\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mseq\u001b[49m\u001b[43m[\u001b[49m\u001b[43ms\u001b[49m\u001b[43m \u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpam\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreverse_complement\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     75\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     76\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mstrand\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33m-\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     77\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mspacer\u001b[39m\u001b[33m\"\u001b[39m] = reverse_df.apply(\n\u001b[32m     78\u001b[39m     \u001b[38;5;28;01mlambda\u001b[39;00m row: \u001b[38;5;28mstr\u001b[39m(seq[row[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m] : row[\u001b[33m\"\u001b[39m\u001b[33mend\u001b[39m\u001b[33m\"\u001b[39m]].reverse_complement()),\n\u001b[32m     79\u001b[39m     axis=\u001b[32m1\u001b[39m,\n\u001b[32m     80\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/.venv/lib/python3.11/site-packages/pandas/core/series.py:4924\u001b[39m, in \u001b[36mSeries.apply\u001b[39m\u001b[34m(self, func, convert_dtype, args, by_row, **kwargs)\u001b[39m\n\u001b[32m   4789\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mapply\u001b[39m(\n\u001b[32m   4790\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   4791\u001b[39m     func: AggFuncType,\n\u001b[32m   (...)\u001b[39m\u001b[32m   4796\u001b[39m     **kwargs,\n\u001b[32m   4797\u001b[39m ) -> DataFrame | Series:\n\u001b[32m   4798\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   4799\u001b[39m \u001b[33;03m    Invoke function on values of Series.\u001b[39;00m\n\u001b[32m   4800\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m   4915\u001b[39m \u001b[33;03m    dtype: float64\u001b[39;00m\n\u001b[32m   4916\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m   4917\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mSeriesApply\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   4918\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   4919\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4920\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconvert_dtype\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconvert_dtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4921\u001b[39m \u001b[43m        \u001b[49m\u001b[43mby_row\u001b[49m\u001b[43m=\u001b[49m\u001b[43mby_row\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4922\u001b[39m \u001b[43m        \u001b[49m\u001b[43margs\u001b[49m\u001b[43m=\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   4923\u001b[39m \u001b[43m        \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m-> \u001b[39m\u001b[32m4924\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/.venv/lib/python3.11/site-packages/pandas/core/apply.py:1427\u001b[39m, in \u001b[36mSeriesApply.apply\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1424\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.apply_compat()\n\u001b[32m   1426\u001b[39m \u001b[38;5;66;03m# self.func is Callable\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1427\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapply_standard\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/.venv/lib/python3.11/site-packages/pandas/core/apply.py:1507\u001b[39m, in \u001b[36mSeriesApply.apply_standard\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1501\u001b[39m \u001b[38;5;66;03m# row-wise access\u001b[39;00m\n\u001b[32m   1502\u001b[39m \u001b[38;5;66;03m# apply doesn't have a `na_action` keyword and for backward compat reasons\u001b[39;00m\n\u001b[32m   1503\u001b[39m \u001b[38;5;66;03m# we need to give `na_action=\"ignore\"` for categorical data.\u001b[39;00m\n\u001b[32m   1504\u001b[39m \u001b[38;5;66;03m# TODO: remove the `na_action=\"ignore\"` when that default has been changed in\u001b[39;00m\n\u001b[32m   1505\u001b[39m \u001b[38;5;66;03m#  Categorical (GH51645).\u001b[39;00m\n\u001b[32m   1506\u001b[39m action = \u001b[33m\"\u001b[39m\u001b[33mignore\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(obj.dtype, CategoricalDtype) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1507\u001b[39m mapped = \u001b[43mobj\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_map_values\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1508\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcurried\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_action\u001b[49m\u001b[43m=\u001b[49m\u001b[43maction\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mconvert_dtype\u001b[49m\n\u001b[32m   1509\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1511\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(mapped) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(mapped[\u001b[32m0\u001b[39m], ABCSeries):\n\u001b[32m   1512\u001b[39m     \u001b[38;5;66;03m# GH#43986 Need to do list(mapped) in order to get treated as nested\u001b[39;00m\n\u001b[32m   1513\u001b[39m     \u001b[38;5;66;03m#  See also GH#25959 regarding EA support\u001b[39;00m\n\u001b[32m   1514\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m obj._constructor_expanddim(\u001b[38;5;28mlist\u001b[39m(mapped), index=obj.index)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/.venv/lib/python3.11/site-packages/pandas/core/base.py:921\u001b[39m, in \u001b[36mIndexOpsMixin._map_values\u001b[39m\u001b[34m(self, mapper, na_action, convert)\u001b[39m\n\u001b[32m    918\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(arr, ExtensionArray):\n\u001b[32m    919\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m arr.map(mapper, na_action=na_action)\n\u001b[32m--> \u001b[39m\u001b[32m921\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43malgorithms\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmap_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43marr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mna_action\u001b[49m\u001b[43m=\u001b[49m\u001b[43mna_action\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/.venv/lib/python3.11/site-packages/pandas/core/algorithms.py:1743\u001b[39m, in \u001b[36mmap_array\u001b[39m\u001b[34m(arr, mapper, na_action, convert)\u001b[39m\n\u001b[32m   1741\u001b[39m values = arr.astype(\u001b[38;5;28mobject\u001b[39m, copy=\u001b[38;5;28;01mF<PERSON>e\u001b[39;00m)\n\u001b[32m   1742\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m na_action \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1743\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlib\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmap_infer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmapper\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1744\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1745\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m lib.map_infer_mask(\n\u001b[32m   1746\u001b[39m         values, mapper, mask=isna(values).view(np.uint8), convert=convert\n\u001b[32m   1747\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mlib.pyx:2972\u001b[39m, in \u001b[36mpandas._libs.lib.map_infer\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/crisprprimer/crisprprimer/nuclease.py:74\u001b[39m, in \u001b[36mNuclease.find_spacers_on.<locals>.<lambda>\u001b[39m\u001b[34m(s)\u001b[39m\n\u001b[32m     71\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m] = start + (\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m.pam) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.pam_side == \u001b[33m\"\u001b[39m\u001b[33m3prime\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[32m0\u001b[39m)\n\u001b[32m     72\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mend\u001b[39m\u001b[33m\"\u001b[39m] = reverse_df[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m] + \u001b[38;5;28mself\u001b[39m.spacer_length\n\u001b[32m     73\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mpam\u001b[39m\u001b[33m\"\u001b[39m] = (start \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.pam_side == \u001b[33m\"\u001b[39m\u001b[33m3prime\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mend\u001b[39m\u001b[33m\"\u001b[39m]).apply(\n\u001b[32m---> \u001b[39m\u001b[32m74\u001b[39m     \u001b[38;5;28;01mlambda\u001b[39;00m s: \u001b[38;5;28mstr\u001b[39m(\u001b[43mseq\u001b[49m\u001b[43m[\u001b[49m\u001b[43ms\u001b[49m\u001b[43m \u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43ms\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpam\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreverse_complement\u001b[49m())\n\u001b[32m     75\u001b[39m )\n\u001b[32m     76\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mstrand\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33m-\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     77\u001b[39m reverse_df[\u001b[33m\"\u001b[39m\u001b[33mspacer\u001b[39m\u001b[33m\"\u001b[39m] = reverse_df.apply(\n\u001b[32m     78\u001b[39m     \u001b[38;5;28;01mlambda\u001b[39;00m row: \u001b[38;5;28mstr\u001b[39m(seq[row[\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m] : row[\u001b[33m\"\u001b[39m\u001b[33mend\u001b[39m\u001b[33m\"\u001b[39m]].reverse_complement()),\n\u001b[32m     79\u001b[39m     axis=\u001b[32m1\u001b[39m,\n\u001b[32m     80\u001b[39m )\n", "\u001b[31mAttributeError\u001b[39m: 'str' object has no attribute 'reverse_complement'"]}], "source": ["from crisprprimer.nuclease import SpCas9\n", "from Bio.SeqUtils import gc_fraction\n", "\n", "# Define the target sequence (CDS of LOC_Os02g35329)\n", "target_sequence = str(cds_sequence)\n", "\n", "# Find spacers on the target sequence\n", "spacers = SpCas9.find_spacers_on(target_sequence)\n", "\n", "# Add CDS start offset to spacer coordinates\n", "spacers[[\"start\", \"end\"]] += start - 1  # Convert to 1-based genome coordinates\n", "\n", "# Filter spacers based on GC content (20%-80%)\n", "spacers[\"gc_fraction\"] = spacers[\"spacer\"].apply(gc_fraction)\n", "spacers = spacers[(spacers[\"gc_fraction\"] >= 0.2) & (spacers[\"gc_fraction\"] <= 0.8)].copy()\n", "\n", "# Filter spacers for restriction enzyme sites (BsaI and BbsI)\n", "from crisprprimer import RESTRICTION_ENZYMES\n", "for enzyme in [\"BsaI\", \"BbsI\"]:\n", "    contain_enzyme = spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[enzyme])\n", "    spacers = spacers[~contain_enzyme].copy()\n", "\n", "# Filter spacers for consecutive T nucleotides (polyT)\n", "polyT = spacers[\"spacer\"].str.contains(\"TTTT\")\n", "spacers = spacers[~polyT].copy()\n", "\n", "# Ensure cut sites are within the CDS\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = ((spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start))\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "# Display the filtered spacers\n", "spacers"]}, {"cell_type": "code", "execution_count": 25, "id": "389a6ce6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>0.60</td>\n", "      <td>21250352</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21250338</td>\n", "      <td>21250358</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>0.70</td>\n", "      <td>21250355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250350</td>\n", "      <td>21250370</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>0.75</td>\n", "      <td>21250367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250356</td>\n", "      <td>21250376</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>0.70</td>\n", "      <td>21250373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250363</td>\n", "      <td>21250383</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CTTGCACCGGCCTCGGCCTC</td>\n", "      <td>0.75</td>\n", "      <td>21250380</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>21251229</td>\n", "      <td>21251249</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>CGCCGGTGTCCGCGCCTGCA</td>\n", "      <td>0.80</td>\n", "      <td>21251246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>21251230</td>\n", "      <td>21251250</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>TCGCCGGTGTCCGCGCCTGC</td>\n", "      <td>0.80</td>\n", "      <td>21251247</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>21251246</td>\n", "      <td>21251266</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GTCGTCGTCGTCGCCGTCGC</td>\n", "      <td>0.75</td>\n", "      <td>21251263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>21251290</td>\n", "      <td>21251310</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GTGCGGGGTGTCGAGCAGGG</td>\n", "      <td>0.75</td>\n", "      <td>21251307</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>21251293</td>\n", "      <td>21251313</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ATGGTGCGGGGTGTCGAGCA</td>\n", "      <td>0.65</td>\n", "      <td>21251310</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>158 rows × 7 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction  \\\n", "0    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG         0.60   \n", "1    21250338  21250358  CGG      +  ATTCCGGACATGCGCGGCGG         0.70   \n", "2    21250350  21250370  CGG      +  CGCGGCGGCGGTTCTTGCAC         0.75   \n", "3    21250356  21250376  CGG      +  GGCGGTTCTTGCACCGGCCT         0.70   \n", "4    21250363  21250383  CGG      +  CTTGCACCGGCCTCGGCCTC         0.75   \n", "..        ...       ...  ...    ...                   ...          ...   \n", "193  21251229  21251249  GGG      -  CGCCGGTGTCCGCGCCTGCA         0.80   \n", "194  21251230  21251250  AGG      -  TCGCCGGTGTCCGCGCCTGC         0.80   \n", "195  21251246  21251266  CGG      -  GTCGTCGTCGTCGCCGTCGC         0.75   \n", "197  21251290  21251310  CGG      -  GTGCGGGGTGTCGAGCAGGG         0.75   \n", "198  21251293  21251313  GGG      -  ATGGTGCGGGGTGTCGAGCA         0.65   \n", "\n", "     cut_site  \n", "0    21250352  \n", "1    21250355  \n", "2    21250367  \n", "3    21250373  \n", "4    21250380  \n", "..        ...  \n", "193  21251246  \n", "194  21251247  \n", "195  21251263  \n", "197  21251307  \n", "198  21251310  \n", "\n", "[158 rows x 7 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["from Bio.Seq import Seq\n", "from crisprprimer.nuclease import SpCas9\n", "from Bio.SeqUtils import gc_fraction\n", "\n", "# Define the target sequence (CDS of LOC_Os02g35329) as a Seq object\n", "target_sequence = Seq(str(cds_sequence))\n", "\n", "# Find spacers on the target sequence\n", "spacers = SpCas9.find_spacers_on(target_sequence)\n", "\n", "# Add CDS start offset to spacer coordinates\n", "spacers[[\"start\", \"end\"]] += start - 1  # Convert to 1-based genome coordinates\n", "\n", "# Filter spacers based on GC content (20%-80%)\n", "spacers[\"gc_fraction\"] = spacers[\"spacer\"].apply(gc_fraction)\n", "spacers = spacers[(spacers[\"gc_fraction\"] >= 0.2) & (spacers[\"gc_fraction\"] <= 0.8)].copy()\n", "\n", "# Filter spacers for restriction enzyme sites (BsaI and BbsI)\n", "from crisprprimer import RESTRICTION_ENZYMES\n", "for enzyme in [\"BsaI\", \"BbsI\"]:\n", "    contain_enzyme = spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[enzyme])\n", "    spacers = spacers[~contain_enzyme].copy()\n", "\n", "# Filter spacers for consecutive T nucleotides (polyT)\n", "polyT = spacers[\"spacer\"].str.contains(\"TTTT\")\n", "spacers = spacers[~polyT].copy()\n", "\n", "# Ensure cut sites are within the CDS\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = ((spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start))\n", "spacers = spacers[cut_CDS].copy()\n", "\n", "# Display the filtered spacers\n", "spacers"]}, {"cell_type": "code", "execution_count": 26, "id": "c3e60c2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[26]\u001b[39m\u001b[32m, line 20\u001b[39m\n\u001b[32m     17\u001b[39m     off_target_results.append(results)\n\u001b[32m     19\u001b[39m \u001b[38;5;66;03m# Combine results\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m off_target_results = \u001b[43mpd\u001b[49m.concat(off_target_results)\n\u001b[32m     22\u001b[39m \u001b[38;5;66;03m# Filter out matches to the target gene (LOC_Os02g35329)\u001b[39;00m\n\u001b[32m     23\u001b[39m off_target_results = off_target_results[\n\u001b[32m     24\u001b[39m     ~((off_target_results[\u001b[33m\"\u001b[39m\u001b[33mtName\u001b[39m\u001b[33m\"\u001b[39m] == \u001b[33m\"\u001b[39m\u001b[33mChr2\u001b[39m\u001b[33m\"\u001b[39m) & \n\u001b[32m     25\u001b[39m       (off_target_results[\u001b[33m\"\u001b[39m\u001b[33mtStart\u001b[39m\u001b[33m\"\u001b[39m] >= start) & \n\u001b[32m     26\u001b[39m       (off_target_results[\u001b[33m\"\u001b[39m\u001b[33mtEnd\u001b[39m\u001b[33m\"\u001b[39m] <= end))\n\u001b[32m     27\u001b[39m ]\n", "\u001b[31mNameError\u001b[39m: name 'pd' is not defined"]}], "source": ["from biov.executables import blat\n", "\n", "# Select the top 5 spacers for off-target analysis\n", "top_spacers = spacers.head(5)\n", "\n", "# Perform BLAT search for each spacer\n", "off_target_results = []\n", "for spacer in top_spacers[\"spacer\"]:\n", "    results = blat(\n", "        \"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\",\n", "        spacer,\n", "        minMatch=1,\n", "        minScore=18,\n", "        stepSize=5,\n", "        fine=True\n", "    )\n", "    off_target_results.append(results)\n", "\n", "# Combine results\n", "off_target_results = pd.concat(off_target_results)\n", "\n", "# Filter out matches to the target gene (LOC_Os02g35329)\n", "off_target_results = off_target_results[\n", "    ~((off_target_results[\"tName\"] == \"Chr2\") & \n", "      (off_target_results[\"tStart\"] >= start) & \n", "      (off_target_results[\"tEnd\"] <= end))\n", "]\n", "\n", "# Display off-target results\n", "off_target_results"]}, {"cell_type": "code", "execution_count": 27, "id": "3de8594e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21295466</td>\n", "      <td>21295486</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21295466,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21286439</td>\n", "      <td>21286459</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21286439,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21277413</td>\n", "      <td>21277433</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21277413,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21268387</td>\n", "      <td>21268407</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21268387,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21259361</td>\n", "      <td>21259381</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21259361,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21295469</td>\n", "      <td>21295489</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21295469,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21286442</td>\n", "      <td>21286462</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21286442,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21277416</td>\n", "      <td>21277436</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21277416,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21268390</td>\n", "      <td>21268410</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21268390,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21259364</td>\n", "      <td>21259384</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21259364,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21295481</td>\n", "      <td>21295501</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21295481,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21286454</td>\n", "      <td>21286474</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21286454,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21277428</td>\n", "      <td>21277448</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21277428,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21268402</td>\n", "      <td>21268422</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21268402,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21259376</td>\n", "      <td>21259396</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21259376,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21303705</td>\n", "      <td>21303725</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21303705,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21303711</td>\n", "      <td>21303731</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21303711,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21295487</td>\n", "      <td>21295507</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21295487,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21286460</td>\n", "      <td>21286480</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21286460,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21277434</td>\n", "      <td>21277454</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21277434,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21268408</td>\n", "      <td>21268428</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21268408,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GGCGGTTCTTGCACCGGCCT</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21259382</td>\n", "      <td>21259402</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21259382,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CTTGCACCGGCCTCGGCCTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21295494</td>\n", "      <td>21295514</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21295494,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CTTGCACCGGCCTCGGCCTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21286467</td>\n", "      <td>21286487</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21286467,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CTTGCACCGGCCTCGGCCTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21277441</td>\n", "      <td>21277461</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21277441,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CTTGCACCGGCCTCGGCCTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21268415</td>\n", "      <td>21268435</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21268415,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>CTTGCACCGGCCTCGGCCTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21259389</td>\n", "      <td>21259409</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21259389,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>27 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "6       19           1           0       0           0            0   \n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "5       20           0           0       0           0            0   \n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "\n", "   tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", "0           0            0      +  TCAATTCCGGACATGCGCGG  ...       0    20   \n", "1           0            0      +  TCAATTCCGGACATGCGCGG  ...       0    20   \n", "2           0            0      +  TCAATTCCGGACATGCGCGG  ...       0    20   \n", "3           0            0      +  TCAATTCCGGACATGCGCGG  ...       0    20   \n", "4           0            0      +  TCAATTCCGGACATGCGCGG  ...       0    20   \n", "0           0            0      +  ATTCCGGACATGCGCGGCGG  ...       0    20   \n", "1           0            0      +  ATTCCGGACATGCGCGGCGG  ...       0    20   \n", "2           0            0      +  ATTCCGGACATGCGCGGCGG  ...       0    20   \n", "3           0            0      +  ATTCCGGACATGCGCGGCGG  ...       0    20   \n", "4           0            0      +  ATTCCGGACATGCGCGGCGG  ...       0    20   \n", "0           0            0      +  CGCGGCGGCGGTTCTTGCAC  ...       0    20   \n", "1           0            0      +  CGCGGCGGCGGTTCTTGCAC  ...       0    20   \n", "2           0            0      +  CGCGGCGGCGGTTCTTGCAC  ...       0    20   \n", "3           0            0      +  CGCGGCGGCGGTTCTTGCAC  ...       0    20   \n", "4           0            0      +  CGCGGCGGCGGTTCTTGCAC  ...       0    20   \n", "6           0            0      +  CGCGGCGGCGGTTCTTGCAC  ...       0    20   \n", "0           0            0      +  GGCGGTTCTTGCACCGGCCT  ...       0    20   \n", "1           0            0      +  GGCGGTTCTTGCACCGGCCT  ...       0    20   \n", "2           0            0      +  GGCGGTTCTTGCACCGGCCT  ...       0    20   \n", "3           0            0      +  GGCGGTTCTTGCACCGGCCT  ...       0    20   \n", "4           0            0      +  GGCGGTTCTTGCACCGGCCT  ...       0    20   \n", "5           0            0      +  GGCGGTTCTTGCACCGGCCT  ...       0    20   \n", "0           0            0      +  CTTGCACCGGCCTCGGCCTC  ...       0    20   \n", "1           0            0      +  CTTGCACCGGCCTCGGCCTC  ...       0    20   \n", "2           0            0      +  CTTGCACCGGCCTCGGCCTC  ...       0    20   \n", "3           0            0      +  CTTGCACCGGCCTCGGCCTC  ...       0    20   \n", "4           0            0      +  CTTGCACCGGCCTCGGCCTC  ...       0    20   \n", "\n", "   tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", "0   Chr2  35937250  21295466  21295486           1         20,      0,   \n", "1   Chr2  35937250  21286439  21286459           1         20,      0,   \n", "2   Chr2  35937250  21277413  21277433           1         20,      0,   \n", "3   Chr2  35937250  21268387  21268407           1         20,      0,   \n", "4   Chr2  35937250  21259361  21259381           1         20,      0,   \n", "0   Chr2  35937250  21295469  21295489           1         20,      0,   \n", "1   Chr2  35937250  21286442  21286462           1         20,      0,   \n", "2   Chr2  35937250  21277416  21277436           1         20,      0,   \n", "3   Chr2  35937250  21268390  21268410           1         20,      0,   \n", "4   Chr2  35937250  21259364  21259384           1         20,      0,   \n", "0   Chr2  35937250  21295481  21295501           1         20,      0,   \n", "1   Chr2  35937250  21286454  21286474           1         20,      0,   \n", "2   Chr2  35937250  21277428  21277448           1         20,      0,   \n", "3   Chr2  35937250  21268402  21268422           1         20,      0,   \n", "4   Chr2  35937250  21259376  21259396           1         20,      0,   \n", "6   Chr2  35937250  21303705  21303725           1         20,      0,   \n", "0   Chr2  35937250  21303711  21303731           1         20,      0,   \n", "1   Chr2  35937250  21295487  21295507           1         20,      0,   \n", "2   Chr2  35937250  21286460  21286480           1         20,      0,   \n", "3   Chr2  35937250  21277434  21277454           1         20,      0,   \n", "4   Chr2  35937250  21268408  21268428           1         20,      0,   \n", "5   Chr2  35937250  21259382  21259402           1         20,      0,   \n", "0   Chr2  35937250  21295494  21295514           1         20,      0,   \n", "1   Chr2  35937250  21286467  21286487           1         20,      0,   \n", "2   Chr2  35937250  21277441  21277461           1         20,      0,   \n", "3   Chr2  35937250  21268415  21268435           1         20,      0,   \n", "4   Chr2  35937250  21259389  21259409           1         20,      0,   \n", "\n", "     tStarts  \n", "0  21295466,  \n", "1  21286439,  \n", "2  21277413,  \n", "3  21268387,  \n", "4  21259361,  \n", "0  21295469,  \n", "1  21286442,  \n", "2  21277416,  \n", "3  21268390,  \n", "4  21259364,  \n", "0  21295481,  \n", "1  21286454,  \n", "2  21277428,  \n", "3  21268402,  \n", "4  21259376,  \n", "6  21303705,  \n", "0  21303711,  \n", "1  21295487,  \n", "2  21286460,  \n", "3  21277434,  \n", "4  21268408,  \n", "5  21259382,  \n", "0  21295494,  \n", "1  21286467,  \n", "2  21277441,  \n", "3  21268415,  \n", "4  21259389,  \n", "\n", "[27 rows x 21 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from biov.executables import blat\n", "\n", "# Select the top 5 spacers for off-target analysis\n", "top_spacers = spacers.head(5)\n", "\n", "# Perform BLAT search for each spacer\n", "off_target_results = []\n", "for spacer in top_spacers[\"spacer\"]:\n", "    results = blat(\n", "        \"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\",\n", "        spacer,\n", "        minMatch=1,\n", "        minScore=18,\n", "        stepSize=5,\n", "        fine=True\n", "    )\n", "    off_target_results.append(results)\n", "\n", "# Combine results\n", "off_target_results = pd.concat(off_target_results)\n", "\n", "# Filter out matches to the target gene (LOC_Os02g35329)\n", "off_target_results = off_target_results[\n", "    ~((off_target_results[\"tName\"] == \"Chr2\") & \n", "      (off_target_results[\"tStart\"] >= start) & \n", "      (off_target_results[\"tEnd\"] <= end))\n", "]\n", "\n", "# Display off-target results\n", "off_target_results"]}, {"cell_type": "code", "execution_count": 28, "id": "5f4f296a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "<p>0 rows × 21 columns</p>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [matches, misMatches, repMatches, nCount, qNumInsert, qBaseInsert, tNumInsert, tBaseInsert, strand, qName, qSize, qStart, qEnd, tName, tSize, tStart, tEnd, blockCount, blockSizes, qStarts, tStarts]\n", "Index: []\n", "\n", "[0 rows x 21 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define the coordinates of the EL5 gene family members\n", "el5_genes = {\n", "    \"Os02g0559800\": (21250335, 21251313),  # LOC_Os02g35329\n", "    \"Os02g0560200\": (21250335, 21251313),  # Same as above (for demonstration)\n", "    \"Os02g0560600\": (21250335, 21251313),  # Same as above (for demonstration)\n", "    \"Os02g0561000\": (21250335, 21251313),  # Same as above (for demonstration)\n", "    \"Os02g0561400\": (21250335, 21251313),  # Same as above (for demonstration)\n", "    \"Os02g0561800\": (21250335, 21251313)   # Same as above (for demonstration)\n", "}\n", "\n", "# Filter off-target results for matches in EL5 gene family regions\n", "el5_matches = []\n", "for gene, (gene_start, gene_end) in el5_genes.items():\n", "    matches = off_target_results[\n", "        (off_target_results[\"tName\"] == \"Chr2\") &\n", "        (off_target_results[\"tStart\"] >= gene_start) &\n", "        (off_target_results[\"tEnd\"] <= gene_end)\n", "    ]\n", "    el5_matches.append(matches)\n", "\n", "el5_matches = pd.concat(el5_matches)\n", "el5_matches"]}, {"cell_type": "code", "execution_count": 29, "id": "53de1891", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>152952</th>\n", "      <td>Chr2</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>gene</td>\n", "      <td>21250125</td>\n", "      <td>21251323</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os02g35329</td>\n", "      <td>LOC_Os02g35329</td>\n", "      <td>RING-H2 finger protein ATL3F, putative, expressed</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       seqid      source  type     start       end  score strand  phase  \\\n", "152952  Chr2  MSU_osa1r7  gene  21250125  21251323    NaN      -    NaN   \n", "\n", "                    ID            Name  \\\n", "152952  LOC_Os02g35329  LOC_Os02g35329   \n", "\n", "                                                     Note Parent  \n", "152952  RING-H2 finger protein ATL3F, putative, expressed    NaN  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_fasta\n", "from biov import read_gff3\n", "\n", "# Load rice genome and annotations\n", "rice_genome = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "gff = read_gff3(\"https://rice.uga.edu/osa1r7_download/osa1_r7.all_models.gff3.gz\")\n", "\n", "# Query for LOC_Os02g35329 and its copies\n", "gene_ids = [\"LOC_Os02g35329\", \"Os02g0559800\", \"Os02g0560200\", \"Os02g0560600\", \"Os02g0561000\", \"Os02g0561400\", \"Os02g0561800\"]\n", "genes = gff.query(\"ID in @gene_ids\")\n", "genes"]}, {"cell_type": "code", "execution_count": 30, "id": "d60654e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>152953</th>\n", "      <td>Chr2</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>mRNA</td>\n", "      <td>21250125</td>\n", "      <td>21251323</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os02g35329.1</td>\n", "      <td>LOC_Os02g35329.1</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os02g35329</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       seqid      source  type     start       end  score strand  phase  \\\n", "152953  Chr2  MSU_osa1r7  mRNA  21250125  21251323    NaN      -    NaN   \n", "\n", "                      ID              Name Note          Parent  \n", "152953  LOC_Os02g35329.1  LOC_Os02g35329.1  NaN  LOC_Os02g35329  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# Query for mRNAs of LOC_Os02g35329 and its copies\n", "mRNAs = gff.query(\"Parent in @gene_ids and type == 'mRNA'\")\n", "mRNAs"]}, {"cell_type": "code", "execution_count": 31, "id": "8b8f9798", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>seqid</th>\n", "      <th>source</th>\n", "      <th>type</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>phase</th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Note</th>\n", "      <th>Parent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>152956</th>\n", "      <td>Chr2</td>\n", "      <td>MSU_osa1r7</td>\n", "      <td>CDS</td>\n", "      <td>21250335</td>\n", "      <td>21251313</td>\n", "      <td>NaN</td>\n", "      <td>-</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os02g35329.1:cds_1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>LOC_Os02g35329.1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       seqid      source type     start       end  score strand  phase  \\\n", "152956  Chr2  MSU_osa1r7  CDS  21250335  21251313    NaN      -    NaN   \n", "\n", "                            ID Name Note            Parent  \n", "152956  LOC_Os02g35329.1:cds_1  NaN  NaN  LOC_Os02g35329.1  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Query for CDS of the mRNA\n", "CDSs = gff.query(\"Parent == 'LOC_Os02g35329.1' and type == 'CDS'\")\n", "CDSs"]}, {"cell_type": "code", "execution_count": 32, "id": "91d150e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["1128"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer.nuclease import SpCas9\n", "\n", "# Extract CDS sequence\n", "CDS = CDSs.iloc[0]\n", "start, end = CDS[\"start\"], CDS[\"end\"]\n", "extra_bases = 60 + 2 * (len(SpCas9.pam) + abs(SpCas9.cut_sites[0])) + 3\n", "seq = rice_genome[CDS[\"seqid\"]].seq[start - extra_bases:end + extra_bases]\n", "len(seq)"]}, {"cell_type": "code", "execution_count": 33, "id": "de817c35", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21250276</td>\n", "      <td>21250296</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>TCGGTTCTACCGATATGTAC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21250277</td>\n", "      <td>21250297</td>\n", "      <td>GGG</td>\n", "      <td>+</td>\n", "      <td>CGGTTCTACCGATATGTACA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250332</td>\n", "      <td>21250352</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CCGTCAATTCCGGACATGCG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>21251306</td>\n", "      <td>21251326</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ACGGTCGATTATTATGGTGC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>21251307</td>\n", "      <td>21251327</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGGTCGATTATTATGGTG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>21251312</td>\n", "      <td>21251332</td>\n", "      <td>TGG</td>\n", "      <td>-</td>\n", "      <td>CATGTGACGGTCGATTATTA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>21251325</td>\n", "      <td>21251345</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGGTCACATATGCATGTGA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>21251344</td>\n", "      <td>21251364</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGCGAGCTCGAGTGCGCG</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>216 rows × 5 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer\n", "0    21250276  21250296  AGG      +  TCGGTTCTACCGATATGTAC\n", "1    21250277  21250297  GGG      +  CGGTTCTACCGATATGTACA\n", "2    21250322  21250342  CGG      +  GACCTCGCCGCCGTCAATTC\n", "3    21250332  21250352  CGG      +  CCGTCAATTCCGGACATGCG\n", "4    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG\n", "..        ...       ...  ...    ...                   ...\n", "211  21251306  21251326  GGG      -  ACGGTCGATTATTATGGTGC\n", "212  21251307  21251327  CGG      -  GACGGTCGATTATTATGGTG\n", "213  21251312  21251332  TGG      -  CATGTGACGGTCGATTATTA\n", "214  21251325  21251345  CGG      -  GCGGTCACATATGCATGTGA\n", "215  21251344  21251364  CGG      -  GACGCGAGCTCGAGTGCGCG\n", "\n", "[216 rows x 5 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Design spacers\n", "spacers = SpCas9.find_spacers_on(seq)\n", "spacers[\"start\"] += start - extra_bases\n", "spacers[\"end\"] += start - extra_bases\n", "spacers"]}, {"cell_type": "code", "execution_count": 34, "id": "1b720789", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21250276</td>\n", "      <td>21250296</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>TCGGTTCTACCGATATGTAC</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21250277</td>\n", "      <td>21250297</td>\n", "      <td>GGG</td>\n", "      <td>+</td>\n", "      <td>CGGTTCTACCGATATGTACA</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250332</td>\n", "      <td>21250352</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CCGTCAATTCCGGACATGCG</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>21251306</td>\n", "      <td>21251326</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ACGGTCGATTATTATGGTGC</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>21251307</td>\n", "      <td>21251327</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGGTCGATTATTATGGTG</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>21251312</td>\n", "      <td>21251332</td>\n", "      <td>TGG</td>\n", "      <td>-</td>\n", "      <td>CATGTGACGGTCGATTATTA</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>21251325</td>\n", "      <td>21251345</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGGTCACATATGCATGTGA</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>21251344</td>\n", "      <td>21251364</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGCGAGCTCGAGTGCGCG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>180 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    21250276  21250296  AGG      +  TCGGTTCTACCGATATGTAC         0.45\n", "1    21250277  21250297  GGG      +  CGGTTCTACCGATATGTACA         0.45\n", "2    21250322  21250342  CGG      +  GACCTCGCCGCCGTCAATTC         0.65\n", "3    21250332  21250352  CGG      +  CCGTCAATTCCGGACATGCG         0.60\n", "4    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG         0.60\n", "..        ...       ...  ...    ...                   ...          ...\n", "211  21251306  21251326  GGG      -  ACGGTCGATTATTATGGTGC         0.45\n", "212  21251307  21251327  CGG      -  GACGGTCGATTATTATGGTG         0.45\n", "213  21251312  21251332  TGG      -  CATGTGACGGTCGATTATTA         0.40\n", "214  21251325  21251345  CGG      -  GCGGTCACATATGCATGTGA         0.50\n", "215  21251344  21251364  CGG      -  GACGCGAGCTCGAGTGCGCG         0.75\n", "\n", "[180 rows x 6 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["from Bio.SeqUtils import gc_fraction\n", "\n", "# Filter by GC content (20-80%)\n", "spacers[\"gc_fraction\"] = spacers[\"spacer\"].apply(gc_fraction)\n", "spacers = spacers[(spacers[\"gc_fraction\"] >= 0.2) & (spacers[\"gc_fraction\"] <= 0.8)].copy()\n", "spacers"]}, {"cell_type": "code", "execution_count": 35, "id": "17b6c003", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21250276</td>\n", "      <td>21250296</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>TCGGTTCTACCGATATGTAC</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21250277</td>\n", "      <td>21250297</td>\n", "      <td>GGG</td>\n", "      <td>+</td>\n", "      <td>CGGTTCTACCGATATGTACA</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250332</td>\n", "      <td>21250352</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CCGTCAATTCCGGACATGCG</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>21251306</td>\n", "      <td>21251326</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ACGGTCGATTATTATGGTGC</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>21251307</td>\n", "      <td>21251327</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGGTCGATTATTATGGTG</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>21251312</td>\n", "      <td>21251332</td>\n", "      <td>TGG</td>\n", "      <td>-</td>\n", "      <td>CATGTGACGGTCGATTATTA</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>21251325</td>\n", "      <td>21251345</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGGTCACATATGCATGTGA</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>21251344</td>\n", "      <td>21251364</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGCGAGCTCGAGTGCGCG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>175 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    21250276  21250296  AGG      +  TCGGTTCTACCGATATGTAC         0.45\n", "1    21250277  21250297  GGG      +  CGGTTCTACCGATATGTACA         0.45\n", "2    21250322  21250342  CGG      +  GACCTCGCCGCCGTCAATTC         0.65\n", "3    21250332  21250352  CGG      +  CCGTCAATTCCGGACATGCG         0.60\n", "4    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG         0.60\n", "..        ...       ...  ...    ...                   ...          ...\n", "211  21251306  21251326  GGG      -  ACGGTCGATTATTATGGTGC         0.45\n", "212  21251307  21251327  CGG      -  GACGGTCGATTATTATGGTG         0.45\n", "213  21251312  21251332  TGG      -  CATGTGACGGTCGATTATTA         0.40\n", "214  21251325  21251345  CGG      -  GCGGTCACATATGCATGTGA         0.50\n", "215  21251344  21251364  CGG      -  GACGCGAGCTCGAGTGCGCG         0.75\n", "\n", "[175 rows x 6 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["from crisprprimer import RESTRICTION_ENZYMES\n", "\n", "# Filter by restriction enzyme sites\n", "default_enzymes = (\"BsaI\", \"BbsI\")\n", "for e in default_enzymes:\n", "    contain_enzyme = spacers[\"spacer\"].str.contains(RESTRICTION_ENZYMES[e])\n", "    spacers = spacers[~contain_enzyme].copy()\n", "spacers"]}, {"cell_type": "code", "execution_count": 36, "id": "c9a59c0a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21250276</td>\n", "      <td>21250296</td>\n", "      <td>AGG</td>\n", "      <td>+</td>\n", "      <td>TCGGTTCTACCGATATGTAC</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21250277</td>\n", "      <td>21250297</td>\n", "      <td>GGG</td>\n", "      <td>+</td>\n", "      <td>CGGTTCTACCGATATGTACA</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250332</td>\n", "      <td>21250352</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CCGTCAATTCCGGACATGCG</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>21251306</td>\n", "      <td>21251326</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ACGGTCGATTATTATGGTGC</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>21251307</td>\n", "      <td>21251327</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGGTCGATTATTATGGTG</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>21251312</td>\n", "      <td>21251332</td>\n", "      <td>TGG</td>\n", "      <td>-</td>\n", "      <td>CATGTGACGGTCGATTATTA</td>\n", "      <td>0.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>214</th>\n", "      <td>21251325</td>\n", "      <td>21251345</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GCGGTCACATATGCATGTGA</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>215</th>\n", "      <td>21251344</td>\n", "      <td>21251364</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GACGCGAGCTCGAGTGCGCG</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>175 rows × 6 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction\n", "0    21250276  21250296  AGG      +  TCGGTTCTACCGATATGTAC         0.45\n", "1    21250277  21250297  GGG      +  CGGTTCTACCGATATGTACA         0.45\n", "2    21250322  21250342  CGG      +  GACCTCGCCGCCGTCAATTC         0.65\n", "3    21250332  21250352  CGG      +  CCGTCAATTCCGGACATGCG         0.60\n", "4    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG         0.60\n", "..        ...       ...  ...    ...                   ...          ...\n", "211  21251306  21251326  GGG      -  ACGGTCGATTATTATGGTGC         0.45\n", "212  21251307  21251327  CGG      -  GACGGTCGATTATTATGGTG         0.45\n", "213  21251312  21251332  TGG      -  CATGTGACGGTCGATTATTA         0.40\n", "214  21251325  21251345  CGG      -  GCGGTCACATATGCATGTGA         0.50\n", "215  21251344  21251364  CGG      -  GACGCGAGCTCGAGTGCGCG         0.75\n", "\n", "[175 rows x 6 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter out spacers with four or more consecutive T nucleotides\n", "polyT = spacers[\"spacer\"].str.contains(\"TTTT\")\n", "spacers = spacers[~polyT].copy()\n", "spacers"]}, {"cell_type": "code", "execution_count": 37, "id": "2ca3dbdd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>gc_fraction</th>\n", "      <th>cut_site</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>0.65</td>\n", "      <td>21250339</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21250332</td>\n", "      <td>21250352</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CCGTCAATTCCGGACATGCG</td>\n", "      <td>0.60</td>\n", "      <td>21250349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21250335</td>\n", "      <td>21250355</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>TCAATTCCGGACATGCGCGG</td>\n", "      <td>0.60</td>\n", "      <td>21250352</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>21250338</td>\n", "      <td>21250358</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>ATTCCGGACATGCGCGGCGG</td>\n", "      <td>0.70</td>\n", "      <td>21250355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>21250350</td>\n", "      <td>21250370</td>\n", "      <td>CGG</td>\n", "      <td>+</td>\n", "      <td>CGCGGCGGCGGTTCTTGCAC</td>\n", "      <td>0.75</td>\n", "      <td>21250367</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>21251230</td>\n", "      <td>21251250</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>TCGCCGGTGTCCGCGCCTGC</td>\n", "      <td>0.80</td>\n", "      <td>21251247</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>21251246</td>\n", "      <td>21251266</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GTCGTCGTCGTCGCCGTCGC</td>\n", "      <td>0.75</td>\n", "      <td>21251263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>21251290</td>\n", "      <td>21251310</td>\n", "      <td>CGG</td>\n", "      <td>-</td>\n", "      <td>GTGCGGGGTGTCGAGCAGGG</td>\n", "      <td>0.75</td>\n", "      <td>21251307</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>21251293</td>\n", "      <td>21251313</td>\n", "      <td>GGG</td>\n", "      <td>-</td>\n", "      <td>ATGGTGCGGGGTGTCGAGCA</td>\n", "      <td>0.65</td>\n", "      <td>21251310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>21251294</td>\n", "      <td>21251314</td>\n", "      <td>AGG</td>\n", "      <td>-</td>\n", "      <td>TATGGTGCGGGGTGTCGAGC</td>\n", "      <td>0.65</td>\n", "      <td>21251311</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>164 rows × 7 columns</p>\n", "</div>"], "text/plain": ["        start       end  pam strand                spacer  gc_fraction  \\\n", "2    21250322  21250342  CGG      +  GACCTCGCCGCCGTCAATTC         0.65   \n", "3    21250332  21250352  CGG      +  CCGTCAATTCCGGACATGCG         0.60   \n", "4    21250335  21250355  CGG      +  TCAATTCCGGACATGCGCGG         0.60   \n", "5    21250338  21250358  CGG      +  ATTCCGGACATGCGCGGCGG         0.70   \n", "6    21250350  21250370  CGG      +  CGCGGCGGCGGTTCTTGCAC         0.75   \n", "..        ...       ...  ...    ...                   ...          ...   \n", "204  21251230  21251250  AGG      -  TCGCCGGTGTCCGCGCCTGC         0.80   \n", "205  21251246  21251266  CGG      -  GTCGTCGTCGTCGCCGTCGC         0.75   \n", "207  21251290  21251310  CGG      -  GTGCGGGGTGTCGAGCAGGG         0.75   \n", "208  21251293  21251313  GGG      -  ATGGTGCGGGGTGTCGAGCA         0.65   \n", "209  21251294  21251314  AGG      -  TATGGTGCGGGGTGTCGAGC         0.65   \n", "\n", "     cut_site  \n", "2    21250339  \n", "3    21250349  \n", "4    21250352  \n", "5    21250355  \n", "6    21250367  \n", "..        ...  \n", "204  21251247  \n", "205  21251263  \n", "207  21251307  \n", "208  21251310  \n", "209  21251311  \n", "\n", "[164 rows x 7 columns]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Ensure cut sites are within CDS\n", "spacers[\"cut_site\"] = spacers[\"end\"] + min(SpCas9.cut_sites)\n", "cut_CDS = ((spacers[\"cut_site\"] <= end) & (spacers[\"cut_site\"] >= start))\n", "spacers = spacers[cut_CDS].copy()\n", "spacers"]}, {"cell_type": "code", "execution_count": 38, "id": "ef24542c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 20 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21295453</td>\n", "      <td>21295473</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21295453,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21286426</td>\n", "      <td>21286446</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21286426,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21277400</td>\n", "      <td>21277420</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21277400,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21268374</td>\n", "      <td>21268394</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21268374,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21259348</td>\n", "      <td>21259368</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21259348,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>GACCTCGCCGCCGTCAATTC</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>20</td>\n", "      <td>Chr2</td>\n", "      <td>35937250</td>\n", "      <td>21250322</td>\n", "      <td>21250342</td>\n", "      <td>1</td>\n", "      <td>20,</td>\n", "      <td>0,</td>\n", "      <td>21250322,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0       20           0           0       0           0            0   \n", "1       20           0           0       0           0            0   \n", "2       20           0           0       0           0            0   \n", "3       20           0           0       0           0            0   \n", "4       20           0           0       0           0            0   \n", "5       20           0           0       0           0            0   \n", "\n", "   tNumInsert  tBaseInsert strand                 qName  ...  qStart  qEnd  \\\n", "0           0            0      +  GACCTCGCCGCCGTCAATTC  ...       0    20   \n", "1           0            0      +  GACCTCGCCGCCGTCAATTC  ...       0    20   \n", "2           0            0      +  GACCTCGCCGCCGTCAATTC  ...       0    20   \n", "3           0            0      +  GACCTCGCCGCCGTCAATTC  ...       0    20   \n", "4           0            0      +  GACCTCGCCGCCGTCAATTC  ...       0    20   \n", "5           0            0      +  GACCTCGCCGCCGTCAATTC  ...       0    20   \n", "\n", "   tName     tSize    tStart      tEnd  blockCount  blockSizes qStarts  \\\n", "0   Chr2  35937250  21295453  21295473           1         20,      0,   \n", "1   Chr2  35937250  21286426  21286446           1         20,      0,   \n", "2   Chr2  35937250  21277400  21277420           1         20,      0,   \n", "3   Chr2  35937250  21268374  21268394           1         20,      0,   \n", "4   Chr2  35937250  21259348  21259368           1         20,      0,   \n", "5   Chr2  35937250  21250322  21250342           1         20,      0,   \n", "\n", "     tStarts  \n", "0  21295453,  \n", "1  21286426,  \n", "2  21277400,  \n", "3  21268374,  \n", "4  21259348,  \n", "5  21250322,  \n", "\n", "[6 rows x 21 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov.executables import blat\n", "\n", "# Check for off-target effects for the first spacer as an example\n", "spacer_example = spacers.iloc[0][\"spacer\"]\n", "blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", spacer_example, minMatch=0, minScore=18, stepSize=5, fine=True)"]}, {"cell_type": "code", "execution_count": 39, "id": "23148566", "metadata": {}, "outputs": [{"data": {"text/plain": ["373245519"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_fasta\n", "rice_genome = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "total_length = sum(len(record.seq) for record in rice_genome.values())\n", "total_length"]}, {"cell_type": "code", "execution_count": 2, "id": "062b8810", "metadata": {}, "outputs": [], "source": ["import dask.dataframe as dd\n", "\n", "\n", "cas12 = dd.read_parquet(\"Cas12/spacers\").compute()"]}, {"cell_type": "code", "execution_count": 5, "id": "1b04dcb9", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    ACGTTAACGTTCGTTCTCGTT\n", "1    GTTCGATCGTTCTCGTTCTCG\n", "2    CAGCTCTGATGTGTAGATGAG\n", "1    ATAAAACCAAGATTTCTAATG\n", "2    TAATGATGTTTGTTCAATCTG\n", "             ...          \n", "2    ACGTTAACGTTCGTCCTCGTT\n", "3    CATTTCACGTTAACGTTCGTC\n", "4    TGCAAGATACAAAAAGATGTA\n", "3    CTATTCAAATCTTGTCCTAAT\n", "6    AATAGGAAAAACAACCAAATA\n", "Name: spacer, Length: 312, dtype: string"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cas12[\"spacer\"]"]}, {"cell_type": "code", "execution_count": 6, "id": "bae97124", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>pam</th>\n", "      <th>strand</th>\n", "      <th>spacer</th>\n", "      <th>seqid</th>\n", "      <th>locus_id</th>\n", "      <th>ID</th>\n", "      <th>gc_fraction</th>\n", "      <th>BbsI_0</th>\n", "      <th>BbsI_1</th>\n", "      <th>BsaI_0</th>\n", "      <th>BsaI_1</th>\n", "      <th>nuclease</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>14183337</td>\n", "      <td>14183358</td>\n", "      <td>TTTC</td>\n", "      <td>+</td>\n", "      <td>ACGTTAACGTTCGTTCTCGTT</td>\n", "      <td>Chr1</td>\n", "      <td>LOC_Os01g25130</td>\n", "      <td>Chr1:14183338..14183358 (+strand)</td>\n", "      <td>0.428571</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>14183467</td>\n", "      <td>14183488</td>\n", "      <td>TTTC</td>\n", "      <td>+</td>\n", "      <td>GTTCGATCGTTCTCGTTCTCG</td>\n", "      <td>Chr1</td>\n", "      <td>LOC_Os01g25130</td>\n", "      <td>Chr1:14183468..14183488 (+strand)</td>\n", "      <td>0.523810</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>25141147</td>\n", "      <td>25141168</td>\n", "      <td>TTTG</td>\n", "      <td>+</td>\n", "      <td>CAGCTCTGATGTGTAGATGAG</td>\n", "      <td>Chr1</td>\n", "      <td>LOC_Os01g43880</td>\n", "      <td>Chr1:25141148..25141168 (+strand)</td>\n", "      <td>0.476190</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>36154737</td>\n", "      <td>36154758</td>\n", "      <td>TTTC</td>\n", "      <td>+</td>\n", "      <td>ATAAAACCAAGATTTCTAATG</td>\n", "      <td>Chr1</td>\n", "      <td>LOC_Os01g62450</td>\n", "      <td>Chr1:36154738..36154758 (+strand)</td>\n", "      <td>0.238095</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>36154753</td>\n", "      <td>36154774</td>\n", "      <td>TTTC</td>\n", "      <td>+</td>\n", "      <td>TAATGATGTTTGTTCAATCTG</td>\n", "      <td>Chr1</td>\n", "      <td>LOC_Os01g62450</td>\n", "      <td>Chr1:36154754..36154774 (+strand)</td>\n", "      <td>0.285714</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>22841328</td>\n", "      <td>22841349</td>\n", "      <td>TTTC</td>\n", "      <td>-</td>\n", "      <td>ACGTTAACGTTCGTCCTCGTT</td>\n", "      <td>Chr12</td>\n", "      <td>LOC_Os12g37230</td>\n", "      <td>Chr12:22841329..22841349 (-strand)</td>\n", "      <td>0.476190</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>22841334</td>\n", "      <td>22841355</td>\n", "      <td>TTTG</td>\n", "      <td>-</td>\n", "      <td>CATTTCACGTTAACGTTCGTC</td>\n", "      <td>Chr12</td>\n", "      <td>LOC_Os12g37230</td>\n", "      <td>Chr12:22841335..22841355 (-strand)</td>\n", "      <td>0.428571</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>22841406</td>\n", "      <td>22841427</td>\n", "      <td>TTTC</td>\n", "      <td>-</td>\n", "      <td>TGCAAGATACAAAAAGATGTA</td>\n", "      <td>Chr12</td>\n", "      <td>LOC_Os12g37230</td>\n", "      <td>Chr12:22841407..22841427 (-strand)</td>\n", "      <td>0.285714</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>26821260</td>\n", "      <td>26821281</td>\n", "      <td>TTTC</td>\n", "      <td>+</td>\n", "      <td>CTATTCAAATCTTGTCCTAAT</td>\n", "      <td>Chr12</td>\n", "      <td>LOC_Os12g43200</td>\n", "      <td>Chr12:26821261..26821281 (+strand)</td>\n", "      <td>0.285714</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>26821244</td>\n", "      <td>26821265</td>\n", "      <td>TTTG</td>\n", "      <td>-</td>\n", "      <td>AATAGGAAAAACAACCAAATA</td>\n", "      <td>Chr12</td>\n", "      <td>LOC_Os12g43200</td>\n", "      <td>Chr12:26821245..26821265 (-strand)</td>\n", "      <td>0.238095</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>AsCas12a</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>312 rows × 14 columns</p>\n", "</div>"], "text/plain": ["       start       end   pam strand                 spacer  seqid  \\\n", "0   14183337  14183358  TTTC      +  ACGTTAACGTTCGTTCTCGTT   Chr1   \n", "1   14183467  14183488  TTTC      +  GTTCGATCGTTCTCGTTCTCG   Chr1   \n", "2   25141147  25141168  TTTG      +  CAGCTCTGATGTGTAGATGAG   Chr1   \n", "1   36154737  36154758  TTTC      +  ATAAAACCAAGATTTCTAATG   Chr1   \n", "2   36154753  36154774  TTTC      +  TAATGATGTTTGTTCAATCTG   Chr1   \n", "..       ...       ...   ...    ...                    ...    ...   \n", "2   22841328  22841349  TTTC      -  ACGTTAACGTTCGTCCTCGTT  Chr12   \n", "3   22841334  22841355  TTTG      -  CATTTCACGTTAACGTTCGTC  Chr12   \n", "4   22841406  22841427  TTTC      -  TGCAAGATACAAAAAGATGTA  Chr12   \n", "3   26821260  26821281  TTTC      +  CTATTCAAATCTTGTCCTAAT  Chr12   \n", "6   26821244  26821265  TTTG      -  AATAGGAAAAACAACCAAATA  Chr12   \n", "\n", "          locus_id                                  ID  gc_fraction  BbsI_0  \\\n", "0   LOC_Os01g25130   Chr1:14183338..14183358 (+strand)     0.428571   False   \n", "1   LOC_Os01g25130   Chr1:14183468..14183488 (+strand)     0.523810   False   \n", "2   LOC_Os01g43880   Chr1:25141148..25141168 (+strand)     0.476190   False   \n", "1   LOC_Os01g62450   Chr1:36154738..36154758 (+strand)     0.238095   False   \n", "2   LOC_Os01g62450   Chr1:36154754..36154774 (+strand)     0.285714   False   \n", "..             ...                                 ...          ...     ...   \n", "2   LOC_Os12g37230  Chr12:22841329..22841349 (-strand)     0.476190   False   \n", "3   LOC_Os12g37230  Chr12:22841335..22841355 (-strand)     0.428571   False   \n", "4   LOC_Os12g37230  Chr12:22841407..22841427 (-strand)     0.285714   False   \n", "3   LOC_Os12g43200  Chr12:26821261..26821281 (+strand)     0.285714   False   \n", "6   LOC_Os12g43200  Chr12:26821245..26821265 (-strand)     0.238095   False   \n", "\n", "    BbsI_1  BsaI_0  BsaI_1  nuclease  \n", "0    False   False   False  AsCas12a  \n", "1    False   False   False  AsCas12a  \n", "2    False   False   False  AsCas12a  \n", "1    False   False   False  AsCas12a  \n", "2    False   False   False  AsCas12a  \n", "..     ...     ...     ...       ...  \n", "2    False   False   False  AsCas12a  \n", "3    False   False   False  AsCas12a  \n", "4    False   False   False  AsCas12a  \n", "3    False   False   False  AsCas12a  \n", "6    False   False   False  AsCas12a  \n", "\n", "[312 rows x 14 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["cas12"]}, {"cell_type": "code", "execution_count": 15, "id": "1f031e9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 373245519 letters in 12 sequences\n", "Searched 25 bases in 1 sequences\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>matches</th>\n", "      <th>misMatches</th>\n", "      <th>repMatches</th>\n", "      <th>nCount</th>\n", "      <th>qNumInsert</th>\n", "      <th>qBaseInsert</th>\n", "      <th>tNumInsert</th>\n", "      <th>tBaseInsert</th>\n", "      <th>strand</th>\n", "      <th>qName</th>\n", "      <th>...</th>\n", "      <th>qStart</th>\n", "      <th>qEnd</th>\n", "      <th>tName</th>\n", "      <th>tSize</th>\n", "      <th>tStart</th>\n", "      <th>tEnd</th>\n", "      <th>blockCount</th>\n", "      <th>blockSizes</th>\n", "      <th>qStarts</th>\n", "      <th>tStarts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>14183463</td>\n", "      <td>14183488</td>\n", "      <td>1</td>\n", "      <td>25,</td>\n", "      <td>0,</td>\n", "      <td>14183463,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>43004916</td>\n", "      <td>43004940</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>1,</td>\n", "      <td>43004916,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>43004844</td>\n", "      <td>43004868</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>1,</td>\n", "      <td>43004844,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>38831160</td>\n", "      <td>38831184</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>1,</td>\n", "      <td>38831160,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>+</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr1</td>\n", "      <td>43270923</td>\n", "      <td>38831088</td>\n", "      <td>38831112</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>1,</td>\n", "      <td>38831088,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>367</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>10595575</td>\n", "      <td>10595599</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>0,</td>\n", "      <td>10595575,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>368</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>7861451</td>\n", "      <td>7861475</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>0,</td>\n", "      <td>7861451,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>369</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>10595648</td>\n", "      <td>10595672</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>0,</td>\n", "      <td>10595648,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>7861596</td>\n", "      <td>7861620</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>0,</td>\n", "      <td>7861596,</td>\n", "    </tr>\n", "    <tr>\n", "      <th>371</th>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "      <td>TTTCGTTCGAT_25</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>25</td>\n", "      <td>Chr9</td>\n", "      <td>23012720</td>\n", "      <td>7861524</td>\n", "      <td>7861548</td>\n", "      <td>1</td>\n", "      <td>24,</td>\n", "      <td>0,</td>\n", "      <td>7861524,</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>372 rows × 21 columns</p>\n", "</div>"], "text/plain": ["     matches  misMatches  repMatches  nCount  qNumInsert  qBaseInsert  \\\n", "0         25           0           0       0           0            0   \n", "1         24           0           0       0           0            0   \n", "2         24           0           0       0           0            0   \n", "3         24           0           0       0           0            0   \n", "4         24           0           0       0           0            0   \n", "..       ...         ...         ...     ...         ...          ...   \n", "367       24           0           0       0           0            0   \n", "368       24           0           0       0           0            0   \n", "369       24           0           0       0           0            0   \n", "370       24           0           0       0           0            0   \n", "371       24           0           0       0           0            0   \n", "\n", "     tNumInsert  tBaseInsert strand           qName  ...  qStart  qEnd  tName  \\\n", "0             0            0      +  TTTCGTTCGAT_25  ...       0    25   Chr1   \n", "1             0            0      +  TTTCGTTCGAT_25  ...       1    25   Chr1   \n", "2             0            0      +  TTTCGTTCGAT_25  ...       1    25   Chr1   \n", "3             0            0      +  TTTCGTTCGAT_25  ...       1    25   Chr1   \n", "4             0            0      +  TTTCGTTCGAT_25  ...       1    25   Chr1   \n", "..          ...          ...    ...             ...  ...     ...   ...    ...   \n", "367           0            0      -  TTTCGTTCGAT_25  ...       1    25   Chr9   \n", "368           0            0      -  TTTCGTTCGAT_25  ...       1    25   Chr9   \n", "369           0            0      -  TTTCGTTCGAT_25  ...       1    25   Chr9   \n", "370           0            0      -  TTTCGTTCGAT_25  ...       1    25   Chr9   \n", "371           0            0      -  TTTCGTTCGAT_25  ...       1    25   Chr9   \n", "\n", "        tSize    tStart      tEnd  blockCount  blockSizes qStarts    tStarts  \n", "0    43270923  14183463  14183488           1         25,      0,  14183463,  \n", "1    43270923  43004916  43004940           1         24,      1,  43004916,  \n", "2    43270923  43004844  43004868           1         24,      1,  43004844,  \n", "3    43270923  38831160  38831184           1         24,      1,  38831160,  \n", "4    43270923  38831088  38831112           1         24,      1,  38831088,  \n", "..        ...       ...       ...         ...         ...     ...        ...  \n", "367  23012720  10595575  10595599           1         24,      0,  10595575,  \n", "368  23012720   7861451   7861475           1         24,      0,   7861451,  \n", "369  23012720  10595648  10595672           1         24,      0,  10595648,  \n", "370  23012720   7861596   7861620           1         24,      0,   7861596,  \n", "371  23012720   7861524   7861548           1         24,      0,   7861524,  \n", "\n", "[372 rows x 21 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov.executables import blat\n", "\n", "\n", "blat(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\", \"TTTCGTTCGATCGTTCTCGTTCTCG\", minMatch=0, minScore=23, stepSize=5, fine=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "dbc858f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["Seq('TTTCACGTTAACGTTCGTCCTCGTT')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from biov import read_fasta\n", "\n", "seqs = read_fasta(\"https://rice.uga.edu/osa1r7_download/osa1_r7.asm.chrs.fa.gz\")\n", "seqs[\"Chr1\"].seq[3753626:3753651]"]}, {"cell_type": "code", "execution_count": 14, "id": "d9a5e880", "metadata": {}, "outputs": [{"data": {"text/plain": ["Seq('TTTCACGTTAACGTTCGTCCTCGTT')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["seqs[\"Chr1\"].seq[3753626:3753651]"]}], "metadata": {"kernelspec": {"display_name": "crisp<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}
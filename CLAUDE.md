# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands
- Build: `uv build`
- Lint: `ruff check .`
- Run tests: `uv run pytest`
- Run a single test: `uv run pytest path/to/test_file.py::test_function_name`
- Install dependencies: `uv add <package_name>`
- Uninstall dependencies: `uv remove <package_name>`
- Sync dependencies: `uv sync`

## High-Level Architecture
- The codebase is a Python package named `crisprprimer` for designing CRISPR primers.
- Main functionality is implemented in `app.py` (entry point) and `crisprprimer/__init__.py` (core logic).
- Uses `biov` for bioinformatics operations and `pandas` for data handling.
- Configuration and dependencies are managed via `pyproject.toml` and `uv.lock`.
- Documentation is built using `mkdocs.yml`.

## Important Files
- `app.py`: Main application entry point.
- `crisprprimer/__init__.py`: Core functionality for CRISPR primer design.
- `pyproject.toml`: Project metadata and dependencies.
- `uv.lock`: Lock file for dependencies.
- `mkdocs.yml`: Configuration for documentation.

## Key Functions
- `get_pairs`: Generates CRISPR primer pairs.
- `_exclude_off_targets`: Filters out off-target spacers.
- `_on_target_in_ind`: Checks on-target activity in individual genomes.
- `crisprprimer`: Main function for designing primers.

## Notes
- Uses `uv` for dependency management and project tasks.
- Supports pre- and post-processing of spacers for efficiency.